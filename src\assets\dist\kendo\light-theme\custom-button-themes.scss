

.k-button-solid-primary:focus, .k-button-solid-primary.k-focus{
    box-shadow: none !important;
}
.kendo-text-button
{
    .k-button-solid-base:focus, .k-button-solid-base.k-focus{
        box-shadow: none !important;
    }
}
.custom-kendo-outline-btn-size
{
    padding-block: 5px;
    width: 100%;
    .k-svg-icon
    {
        width: 16px;
        height: 16px;
    }
}
.kendo-custom-button{
    padding: 6px 16px;
    height: 32px;
    border-radius: 4px;
    .k-button-text{
        @extend .Body-R;
        width: 100%;
    }
}
button[disabled]
{
    border-color: #93B0ED;
    color: #93B0ED;
}
.k-input-custom{
    .k-input-suffix{
        padding-right: 8px;
        .text-search-button{
            background: transparent;
            kendo-svg-icon{
                background: transparent;
                color: $nep-primary;
            }
        }
    }
}
.k-input-custom:focus-within{
box-shadow: none !important;
}
.k-btn-white{
    background: $nep-white;
    &:hover{
        background: $nep-primary;
    }
}
@each $btn-width in 102,180, 220, 240, 300,280 {
    .btn-width-#{$btn-width} {
            width: #{$btn-width}px !important;
    }
}
.k-button-outline-primary{
    &:hover{
        background: $Primary-40 !important;
        color:$Primary-78 !important;
    }
}