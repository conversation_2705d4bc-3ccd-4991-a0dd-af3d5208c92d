import { ComponentFixture, TestBed } from '@angular/core/testing';
import { AutomatedNewsletterComponent } from './automated-newsletter.component';
import { Router } from '@angular/router';
import { RouterTestingModule } from '@angular/router/testing';
import { TemplateBuilderComponentService } from '../services/template-builder-component.service';
import { of } from 'rxjs';

describe('AutomatedNewsletterComponent', () => {
  let component: AutomatedNewsletterComponent;
  let fixture: ComponentFixture<AutomatedNewsletterComponent>;
  let router: Router;
  let mockTemplateService: any;

  beforeEach(async () => {
    mockTemplateService = {
      getTemplateList: jasmine.createSpy('getTemplateList').and.returnValue(of([
        { templateId: 1, templateName: 'Newsletter 1' },
        { templateId: 2, templateName: 'Newsletter 2' }
      ]))
    };

    await TestBed.configureTestingModule({
      declarations: [AutomatedNewsletterComponent],
      imports: [RouterTestingModule.withRoutes([])],
      providers: [
        { provide: TemplateBuilderComponentService, useValue: mockTemplateService }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(AutomatedNewsletterComponent);
    component = fixture.componentInstance;
    router = TestBed.inject(Router);
    fixture.detectChanges();
  });

  beforeEach(() => {
    localStorage.clear();
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

  it('should navigate to new template when template is null', () => {
    const navigateSpy = spyOn(router, 'navigate');
    component.redirectToTemplate(null);
    expect(localStorage.getItem('headerName')).toBe('Create Newsletter Template');
    expect(navigateSpy).toHaveBeenCalledWith(['/clo-newsletter-template']);
  });

  it('should navigate to existing template with templateId', () => {
    const navigateSpy = spyOn(router, 'navigate');
    const mockTemplate = {
      templateName: 'Weekly Update',
      templateUniqueId: "123"
    };
    component.redirectToTemplate(mockTemplate);
    expect(localStorage.getItem('headerName')).toBe('Weekly Update');
    expect(navigateSpy).toHaveBeenCalledWith(['/clo-newsletter-template', "123"]);
  });

  it('should call getTemplateList and set templateList on getTemplates', () => {
    component.templateList = [];
    component.getTemplates();
    expect(mockTemplateService.getTemplateList).toHaveBeenCalled();
    expect(component.templateList.length).toBe(2);
    expect(component.templateList[0].templateName).toBe('Newsletter 1');
  });
});
