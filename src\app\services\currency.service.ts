import { HttpClient } from "@angular/common/http";
import { Inject, Injectable } from "@angular/core";
import { Router } from "@angular/router";
import { throwError } from "rxjs";
import { catchError, map } from "rxjs/operators";

@Injectable()
export class CurrencyService {

  myAppUrl: string = "";

  constructor(private _http: HttpClient,
    @Inject("BASE_URL") baseUrl: string,
    private router: Router) {
    this.myAppUrl = baseUrl;
  }

  errorHandler(error: any) {
    return throwError(error);
  }

  getAllCurrencies() {
    return this._http
      .get<any>(
        this.myAppUrl + "api/master/GetAllCurrencies"
      )
      .pipe(
        map((response) => response),
        catchError((error) => this.errorHandler(error))
      );
  }

  getToCurrencies(isBulkUpload: boolean, currCode: any) {
    return this._http
      .get<any>(
        this.myAppUrl + "api/master/GetToCurrencies?IsBulkUpload=" + isBulkUpload + "&FromCurrencyCode=" + currCode
      )
      .pipe(
        map((response) => response),
        catchError((error) => this.errorHandler(error))
      );
  }

  GetToCurrenciesByFromCurrency(currCode) {
    return this._http
      .get<any>(
        this.myAppUrl + "api/master/GetToCurrenciesByFromCurrency?fromCurrency=" + currCode
      )
      .pipe(
        map((response) => response),
        catchError((error) => this.errorHandler(error))
      );
  }
  GetFxratesBulkUpload() {
    return this._http
      .get<any>(
        this.myAppUrl + "api/master/fxrates"
      )
      .pipe(
        map((response) => response),
        catchError((error) => this.errorHandler(error))
      );
  }
  GetFxratesByPreference(data: any) {
    return this._http
      .post<any>(
        this.myAppUrl + "api/master/currency-rates", data
      )
      .pipe(
        map((response) => response),
        catchError((error) => this.errorHandler(error))
      );
  }
  getFxRateFilterOptions(isBulkUpload: boolean = true) {
    return this._http
      .get<any>(
        this.myAppUrl + "api/master/fxrate-model/" + isBulkUpload
      )
      .pipe(
        map((response) => response),
        catchError((error) => this.errorHandler(error))
      );
  }

  downloadFxRates(data: any) {
    return this._http
      .post<any>(
        this.myAppUrl + "api/master/currency-rates/download", data,
        { responseType: 'blob' as 'json' }
      )
      .pipe(
        map((response) => response),
        catchError((error) => this.errorHandler(error))
      );
  }
}
