
<div class="static-form-container">
    <div>
        <div *ngIf="loading" class="loading-container">
            <div class="spinner-border" role="status">
              <span class="sr-only">Loading...</span>
            </div>
          </div>
        <div class="col-6 pl-0 pr-0" *ngIf="!loading">
            <ng-container *ngFor="let kpiData of tableResult">
                <div class="row align-items-center content-padding bottom-border">
                    <div class="col-6" style="padding-left: 0px;">
                        <div class="form-label Body-R content-height">
                            {{ kpiData.Kpi }}
                        </div>
                    </div>
                    <div class="col-6 border-primary bottom-border">
                        <div class="form-value d-flex justify-content-between align-items-center content-height">
                            <div class="Body-B">
                                <span title="Click here to view logs">
                                    {{ kpiData.KpiInfo }}
                                </span>
                            </div>
                            <div class="edit-icon d-none" id="edit-icon"
                                title="Click here to view logs">
                                <img class="edit-icon-img" src="assets/dist/images/clo-pencil-icon.svg" alt="Edit Icon"
                                    title="Click here to Update Cell Value" />
                            </div>
                        </div>
                    </div>
                </div>
            </ng-container>
        </div>

    </div>
</div>