@import "../../../../../../variables.scss";

// Variables
$primary-color: #4061c7;
$border-color: #e6e6e6;
$header-bg: #fafafa;
$text-dark: #1a1a1a;
$text-gray: #666666;
$border-radius: 4px;

// Layout & Container Styles
.row.mr-0.ml-0 {
  width: 100%;
}

// Static Form Container Styles
.static-form-container {
  width: 100%;
  height: 100%;
  padding: 5px;
  background-color: #ffffff;

  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;

    .spinner-border {
      width: 3rem;
      height: 3rem;
      color: $nep-primary;
    }
  } 
 

  .form-label {
    color: $text-gray;
    font-size: 14px;
    font-weight: 400;
  }

  .form-value {
    color: $text-gray;
    font-size: 14px;
    font-weight: 700;
  }

  .content-height {
    height: 1.5rem;
  }

  .content-padding {
    padding: 0.5rem 1rem;
  }
}

// Utility Classes
.edit-icon {
  cursor: pointer;
  padding-left: 5px;
}


