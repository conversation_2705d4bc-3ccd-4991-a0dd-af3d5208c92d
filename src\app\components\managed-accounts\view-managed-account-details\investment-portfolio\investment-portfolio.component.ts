import { Component, Input, OnChanges, SimpleChanges } from '@angular/core';
import { INavigationTabLink } from '../../shared/navigationLink.model';
import { ManagedAccountPermissionConstants } from 'src/app/common/constants';

@Component({
  selector: 'app-investment-portfolio',
  templateUrl: './investment-portfolio.component.html',
  styleUrls: ['./investment-portfolio.component.scss']
})
export class InvestmentPortfolioComponent implements OnChanges {
  @Input() permissions: any;
  @Input() subPagefieldList: any[];
  @Input() managedAccountId: string;
  public ManagedAccountPermissionConstants = ManagedAccountPermissionConstants;
  public investmentPortfolioTitle: string = '';

  constructor() { }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['subPagefieldList'] && changes['subPagefieldList'].currentValue) {
      this.buildInvestmentPortfolioTitle();
    }
  }

  private buildInvestmentPortfolioTitle() {
    if (this.subPagefieldList && this.subPagefieldList.length > 0) {
      const investmentPortfolioItem = this.subPagefieldList.find(item =>
        item.name === ManagedAccountPermissionConstants.ManagedAccountSubFeature.InvestmentPortfolio
      );

      if (investmentPortfolioItem) {
        this.investmentPortfolioTitle = investmentPortfolioItem.displayName || investmentPortfolioItem.aliasName;
      }
    }
    
  }
}
