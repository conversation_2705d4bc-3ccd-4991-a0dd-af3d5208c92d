<div class="investor-cashflow-container">
  <div class="investor-cashflow-content">
    <kendo-tabstrip id="{{selectedTab}}" (tabSelect)="selectedTabData($event)">
      <ng-container *ngFor="let tab of filteredNavigationTabLinks">
        <kendo-tabstrip-tab 
          [title]="tab.aliasName" 
          [selected]="filteredNavigationTabLinks[0] === tab">
          <ng-template kendoTabContent>
            <app-managed-account-data-table 
              [managedAccountId]="managedAccountId"
              [tableTitle]='tab.aliasName'
              [moduleName]="tab.name"
              [tableName]='tab.tableName'
              [permissions]="permissions"
              [isStaticTable]="tab.isStaticTable">
            </app-managed-account-data-table>
          </ng-template>
        </kendo-tabstrip-tab>
      </ng-container>
    </kendo-tabstrip>
  </div>
</div>