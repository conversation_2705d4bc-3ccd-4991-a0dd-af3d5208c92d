import { Component, Input, OnChanges, SimpleChanges } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ManagedAccountService } from '../../managed-account.service';
import { ToastrService } from 'ngx-toastr';
import { ManagedAccountPermissionConstants, TOASTER_MSG } from 'src/app/common/constants';
import { ManagedAccount } from '../../managed-account.model';
import { INavigationTabLink } from '../../shared/navigationLink.model';

@Component({
  selector: 'app-investment-page',
  templateUrl: './investment-page.component.html',
  styleUrls: ['./investment-page.component.scss']
})
export class InvestmentPageComponent implements OnChanges {
  @Input() permissions: { [subFeature: string]: { canView: boolean; canEdit: boolean } };
  @Input() subPagefieldList: any[];
  @Input() managedAccountId: string;
  isLoading: boolean = true;
  managedAccountData: ManagedAccount;
  public ManagedAccountPermissionConstants = ManagedAccountPermissionConstants;

  public navigationTabLinks: INavigationTabLink[] = [];
  public managedAccountFactsTitle: string = '';
  public investmentSummaryTitle: string = '';
  
  constructor(private route: ActivatedRoute,
    private managedAccountService: ManagedAccountService,
    private toastrService: ToastrService,
    private router: Router) { }

  ngOnInit() {
    this.isLoading = false;
    if (this.managedAccountId) {
      this.loadInvestmentPage(this.managedAccountId);
    }
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['subPagefieldList'] && changes['subPagefieldList'].currentValue) {
      this.buildNavigationTabLinks();
    }
  }

  private buildNavigationTabLinks() {
    if (this.subPagefieldList && this.subPagefieldList.length > 0) {      
      const capTable0 = this.subPagefieldList.find(item => item.name === ManagedAccountPermissionConstants.ManagedAccountSubFeature.ManagedAccountFacts);
      const capTable1 = this.subPagefieldList.find(item => item.name === ManagedAccountPermissionConstants.ManagedAccountSubFeature.InvestmentSummary);
      
      if (capTable0) {
        this.managedAccountFactsTitle = capTable0.displayName || capTable0.aliasName;
      }
      if (capTable1) {
        this.investmentSummaryTitle = capTable1.displayName || capTable1.aliasName;
      }
      const investmentPageTabs = [ManagedAccountPermissionConstants.ManagedAccountSubFeature.Performance,ManagedAccountPermissionConstants.ManagedAccountSubFeature.NAVData, ManagedAccountPermissionConstants.ManagedAccountSubFeature.IncomeDistributionsInvestmentPage];
      const staticTablesTypes = [ManagedAccountPermissionConstants.ManagedAccountSubFeature.NAVData, ManagedAccountPermissionConstants.ManagedAccountSubFeature.IncomeDistributionsInvestmentPage];
      this.navigationTabLinks = this.subPagefieldList
        .filter(item => investmentPageTabs.includes(item.name))
        .map(item => ({
          name: item.name,
          aliasName: item.displayName || item.aliasName,
          tableName: item.name,
          isSelected: item.isSelected || false,
          isStaticTable: staticTablesTypes.includes(item.name)
        }));
    }
  }

  loadInvestmentPage(managedAccountId: string) {
    this.isLoading = true;
    this.managedAccountService.getManagedAccountById(managedAccountId).subscribe({
      next: (data) => {
        this.isLoading = false;
        if (data == null) {
          this.toastrService.error(TOASTER_MSG.NOT_FOUND, "", { positionClass: TOASTER_MSG.POS_CENTER });
          this.router.navigate(['/managed-accounts']);
          return;
        }
        this.managedAccountData = data;
      },
      error: (error) => {
        this.isLoading = false;
        this.toastrService.error(TOASTER_MSG.NOT_FOUND, "", { positionClass: TOASTER_MSG.POS_CENTER });
        this.router.navigate(['/managed-accounts']);
      }
    });
  }
  

  redirectToEditPage(step: number) {
    this.router.navigate(['/add-managed-account', this.managedAccountId], { queryParams: { step: step } });
  }

}

// In your template (investment-page.component.html), use:
// *ngFor="let tab of navigationTabLinks"
// *ngIf="canViewTab(tab)"   <!-- Only show tabs the user can view -->

