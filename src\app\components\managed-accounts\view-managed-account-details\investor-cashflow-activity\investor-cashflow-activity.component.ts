import { Component, Input, OnChanges, SimpleChanges } from '@angular/core';
import { INavigationTabLink } from '../../shared/navigationLink.model';
import { ActivatedRoute } from '@angular/router';
import { ManagedAccount } from '../../managed-account.model';
import { ManagedAccountConstants, ManagedAccountPermissionConstants } from 'src/app/common/constants';

@Component({
  selector: 'app-investor-cashflow-activity',
  templateUrl: './investor-cashflow-activity.component.html',
  styleUrls: ['./investor-cashflow-activity.component.scss']
})
export class InvestorCashflowActivityComponent implements OnChanges {
  @Input() permissions: any;
  @Input() subPagefieldList: any[];
  @Input() managedAccountId: string;
  isLoading: boolean = true;
  accountData: ManagedAccount;
  ManagedAccountConstant = ManagedAccountConstants;
  selectedTab: string = this.ManagedAccountConstant.TAB_NAMES.Investor_Cashflow_Activity;

  public navigationTabLinks: INavigationTabLink[] = [];
  public filteredNavigationTabLinks: INavigationTabLink[] = [];

  constructor() {}

  ngOnInit() {
    this.isLoading = false;
    // Only filter if we already have navigation links from ngOnChanges
    if (this.navigationTabLinks.length > 0) {
      this.filterNavigationLinks();
    }
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['subPagefieldList'] && changes['subPagefieldList'].currentValue) {
      this.buildNavigationTabLinks();
      this.filterNavigationLinks();
    }
  }

  private buildNavigationTabLinks() {
    if (this.subPagefieldList && this.subPagefieldList.length > 0) {
      const cashflowTabs = [ManagedAccountPermissionConstants.ManagedAccountSubFeature.CapitalActivity, ManagedAccountPermissionConstants.ManagedAccountSubFeature.IncomeDistributionInvestorCashflow];
      this.navigationTabLinks = this.subPagefieldList
        .filter(item => cashflowTabs.includes(item.name))
        .map((item, index) => ({
          name: item.name,
          aliasName: item.displayName || item.aliasName,
          tableName: item.name,
          isSelected: index === 0,
          isStaticTable: false
        }));
      }
  }

  private filterNavigationLinks() {
    if (this.navigationTabLinks.length > 0) {
      this.filteredNavigationTabLinks = this.navigationTabLinks.filter(tab => 
        this.permissions?.[tab.name]?.canView
      );
        if (!this.filteredNavigationTabLinks.some(tab => tab.isSelected) && this.filteredNavigationTabLinks.length > 0) {
        this.filteredNavigationTabLinks[0].isSelected = true;
        this.selectedTab = this.filteredNavigationTabLinks[0].name;
      }
    }
  }

  selectedTabData(tab: INavigationTabLink) {
    this.selectedTab = tab.name;
    this.filteredNavigationTabLinks.forEach(navTab => {
      navTab.isSelected = navTab.name === tab.name;
    });
  }
}
