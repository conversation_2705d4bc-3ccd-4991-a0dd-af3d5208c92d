<div class="row performance-section mr-0 ml-0" *ngIf="isEnableView">
    <div class="col-12 col-sm-12 col-md-12 col-xl-12 col-lg-12 outer-section pl-0 pr-0">
        <div class="portfolio-company-table">
            <div class="border-bottom pt-1 pb-1">
                <div class="row mr-0 ml-0">
                    <div class=" col-lg-12 col-md-12 col-sm-12 pl-0 pr-0">
                        <div class="pull-right headerfontsize">
                            <div class="d-inline-block search pr-2">
                                <kendo-textbox [clearButton]="false" [fillMode]="'solid'" [size]="'medium'"
                                    placeholder="Search" class="k-custom-solid-dropdown k-dropdown-height-32"
                                    [(ngModel)]="searchText" (valueChange)="onFilter($event)">
                                    <ng-template kendoTextBoxPrefixTemplate>
                                        <span class="fa fa-search fasearchicon"></span>
                                    </ng-template>
                                </kendo-textbox>
                            </div>
                            <div class="d-inline-block">
                                <div class="d-inline-block pl-1">
                                    <kendo-combobox [data]="fxSource" textField="source" valueField="source"
                                        [fillMode]="'solid'" [valuePrimitive]="true" [clearButton]="false"
                                        [size]="'medium'" [placeholder]="'Select here...'"
                                        class="k-dropdown-width-200 k-custom-solid-dropdown k-dropdown-height-32"
                                        [(ngModel)]="selectedFxSource" (valueChange)="onFxSourceChange($event)">
                                    </kendo-combobox>
                                </div>
                            </div>
                            <div class="d-inline-block cloud_download pl-0" id="div-download-fxrates">
                                <div class="d-inline  p-2 headerfontsize icon-menu mr-2 ml-2"><img id="dropdownMenuButton"
                                        [matMenuTriggerFor]="menu" src="assets/dist/images/FiSliders.svg"
                                        class="cursor-filter" alt="" #masterMenuTrigger="matMenuTrigger" />
                                    <span [matMenuTriggerFor]="menu" #filterMenuTrigger="matMenuTrigger"
                                        class=""></span>
                                </div>
                                <div class="d-inline-block">
                                    <button class="kendo-custom-button Body-R apply-btn" [disabled]="!fxrateslistResults.length" kendoButton
                                        themeColor="primary" (click)="downloadFxRates()">
                                        <span class="pl-1 upload-loader" *ngIf="isLoading">
                                            <i aria-hidden="true"
                                                class="download-circle-loader fa fa-circle-o-notch fa-1x fa-fw"></i>
                                        </span>
                                        Download</button>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
            <div class="">
                <kendo-grid
                    class="k-grid-border-right-width k-grid-border-bottom-width k-grid-outline-none custom-kendo-fxrates-table-grid"
                    [kendoGridBinding]="fxrateslistResults" [scrollable]="true"  [resizable]="true" [sortable]="true">
                    <!-- Basic columns (always shown) -->
                    <kendo-grid-column field="fromCurrencyCode" [width]="180" [sticky]="true"
                        [minResizableWidth]="160" [maxResizableWidth]="300">
                        <ng-template kendoGridHeaderTemplate let-column>
                            <div class="custom-header">
                                <span>From Currency</span>
                                <img src="assets/dist/images/filter-icon.svg" class="cursor-filter ml-1" 
                                    [matMenuTriggerFor]="fromCurrencyMenu" #fromCurrencyMenuTrigger="matMenuTrigger"
                                    (click)="$event.stopPropagation()" />
                            </div>
                        </ng-template>
                        <ng-template kendoGridCellTemplate let-rowData>
                            <div class="content">
                                <span>{{rowData.fromCurrencyCode}}</span>
                            </div>
                        </ng-template>
                    </kendo-grid-column>

                    <kendo-grid-column field="toCurrencyCode" [width]="180" [sticky]="true"
                        [minResizableWidth]="160" [maxResizableWidth]="300">
                        <ng-template kendoGridHeaderTemplate let-column>
                            <div class="custom-header">
                                <span>To Currency</span>
                                <img src="assets/dist/images/filter-icon.svg" class="cursor-filter ml-1" 
                                    [matMenuTriggerFor]="toCurrencyMenu" #toCurrencyMenuTrigger="matMenuTrigger"
                                    (click)="$event.stopPropagation()" />
                            </div>
                        </ng-template>
                        <ng-template kendoGridCellTemplate let-rowData>
                            <div class="content">
                                <span>{{rowData.toCurrencyCode}}</span>
                            </div>
                        </ng-template>
                    </kendo-grid-column>

                    <!-- Dynamic period columns -->
                    <ng-container *ngFor="let period of fxratePeriods">
                        <kendo-grid-column-group [title]="period">
                            <ng-container *ngFor="let methodology of selectedMethodologies">
                                <kendo-grid-column [field]="sanitizeFieldId(period, methodology)" [title]="methodology"
                                    [width]="200" [minResizableWidth]="300" [maxResizableWidth]="400">
                                    <ng-template kendoGridCellTemplate let-rowData>
                                        <span tabindex="0" class="prtcmny-det-o">
                                            <span class="content">
                                                <span
                                                    [title]="getValueForMethodology(rowData, period, methodology,true)">
                                                    {{getValueForMethodology(rowData, period, methodology)}}
                                                </span>
                                            </span>
                                        </span>
                                    </ng-template>
                                </kendo-grid-column>
                            </ng-container>
                        </kendo-grid-column-group>
                    </ng-container>

                    <ng-template kendoGridNoRecordsTemplate>
                            <div class="finacials-beta-empty-state d-flex flex-column justify-content-center" [style.height]="'calc(100vh - 314px)'"> 
                                <div class="row mr-0 ml-0 pt-3 pb-3  empty-pc" >
                                    <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 col-xs-12 pr-0 pl-0 d-flex flex-column justify-content-center">
                                        <div class="text-center">
                                            <img  alt=""  src="assets/dist/images/no-data-fx.svg" class="showHandIcon pr-1 mt-0"/>
                                        </div>
                                        <div class="text-center pt-2 empty-state-text pb-2">
                                            No value available
                                        </div>
                                        <div class="text-center pt-0 empty-state-text">
                                            {{ fxSource =='System API'? 'Please check ‘System API’ or change your source to ‘Bulk Upload’' :'Please check ‘Bulk Upload’ or change your source to ‘System API’'}}
                                      </div>
                                    </div>
                                  </div>
                            </div>
                    </ng-template>
                </kendo-grid>
            </div>
        </div>
    </div>
</div>
<mat-menu #menu="matMenu" [hasBackdrop]="true" matMenuTriggerRestoreFocus="false" class="fixed-fxrates-menu">
    <app-fxrates-popup [selectedFxSource]="selectedFxSource" [fxOptions]="fxOptions"
        (submitForm)="onFxRatesFormSubmit($event)" (cancelForm)="onFxRatesFormCancel($event)"></app-fxrates-popup>
</mat-menu>

<!-- Reusable Currency Filter Template -->
<ng-template #currencyFilterTemplate let-filterType="filterType">
    <div class="filter-menu-container" (click)="$event.stopPropagation()">
        <div class="filter-menu-content">
            <div class="filter-menu-header pb-0">
                <div class="search-box">
                    <input type="text" placeholder="Search here..." 
                        [ngModel]="filterType === 'from' ? fromCurrencySearchText : toCurrencySearchText" 
                        (input)="filterType === 'from' ? onFromCurrencySearch() : onToCurrencySearch()">
                    <span class="search-icon">
                        <i class="fa fa-search"></i>
                    </span>
                </div>
            </div>
            <div class="filter-menu-body">
                <div class="select-all-option mb-0">
                    <mat-checkbox
                        [checked]="filterType === 'from' ? isAllFromCurrenciesSelected : isAllToCurrenciesSelected"
                        [indeterminate]="filterType === 'from' ? isFromCurrenciesIndeterminate : isToCurrenciesIndeterminate"
                        (change)="filterType === 'from' ? toggleAllFromCurrencies($event) : toggleAllToCurrencies($event)"
                        color="primary"
                        class="mat-checkbox-large">
                        <span class="S-R pl-1">Select all</span>
                    </mat-checkbox>
                </div>
                <div class="currency-options">
                    <div *ngFor="let currency of filterType === 'from' ? filteredFromCurrencies : filteredToCurrencies" class="currency-option">
                        <mat-checkbox
                            [checked]="filterType === 'from' ? isFromCurrencySelected(currency.value) : isToCurrencySelected(currency.value)"
                            (change)="filterType === 'from' ? toggleFromCurrency(currency.value, $event) : toggleToCurrency(currency.value, $event)"
                            color="primary"
                            class="mat-checkbox-medium">
                          <span class="pl-1 S-R select-all">  {{currency.text}}</span> 
                        </mat-checkbox>
                    </div>
                </div>
            </div>
            <div class="filter-menu-actions">
                <button type="button" class="kendo-custom-button Body-R apply-btn mr-2" fillMode="outline" kendoButton 
                    (click)="filterType === 'from' ? onCancelFromFilter() : onCancelToFilter()" themeColor="primary">Cancel</button>
                <button class="kendo-custom-button Body-R apply-btn" kendoButton
                    themeColor="primary" (click)="filterType === 'from' ? onApplyFromFilter() : onApplyToFilter()">Apply</button>
            </div>
        </div>
    </div>
</ng-template>

<!-- From Currency Filter Menu -->
<mat-menu #fromCurrencyMenu="matMenu" [hasBackdrop]="true" matMenuTriggerRestoreFocus="false" class="currency-filter-menu">
    <ng-container *ngTemplateOutlet="currencyFilterTemplate; context: { filterType: 'from' }"></ng-container>
</mat-menu>

<!-- To Currency Filter Menu -->
<mat-menu #toCurrencyMenu="matMenu" [hasBackdrop]="true" matMenuTriggerRestoreFocus="false" class="currency-filter-menu">
    <ng-container *ngTemplateOutlet="currencyFilterTemplate; context: { filterType: 'to' }"></ng-container>
</mat-menu>

<app-loader-component *ngIf="isLoader"></app-loader-component>