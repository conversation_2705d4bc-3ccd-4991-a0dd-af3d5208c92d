import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { of, throwError } from 'rxjs';
import { ManagedAccountsCommentaryWrapperComponent } from './commentary-wrapper.component';
import { CommentaryUI } from '../../models/commentary.model';
import { ManagedAccountService } from '../../managed-account.service';
import { AccountService } from '../../../../services/account.service';

describe('ManagedAccountsCommentaryWrapperComponent', () => {
  let component: ManagedAccountsCommentaryWrapperComponent;
  let fixture: ComponentFixture<ManagedAccountsCommentaryWrapperComponent>;
  let mockToastrService: jasmine.SpyObj<ToastrService>;
  let mockManagedAccountService: jasmine.SpyObj<ManagedAccountService>;

  const mockManagedAccountId = 'test-account-123';

  const mockCommentaryResponse = [
    {
      commentaryID: 1,
      commentaryText: '<p>Test GLI Commentary</p>',
      commentaryType: 1
    },
    {
      commentaryID: 2,
      commentaryText: '<p>Test Market Commentary</p>',
      commentaryType: 2
    }
  ];

  const mockCommentary: CommentaryUI = {
    tableId: 24,
    sequenceNo: 1,
    id: 1,
    name: 'Market Commentary',
    newComment: '<p>Test comment</p>',
    commentaryType: 1,
    isExpanded: false,
    isEdit: false
  };

  beforeEach(async () => {
    mockToastrService = jasmine.createSpyObj('ToastrService', ['error', 'success']);
    mockManagedAccountService = jasmine.createSpyObj('ManagedAccountService', [
      'getCommentariesByManagedAccount',
      'saveCommentaries'
    ]);

    await TestBed.configureTestingModule({
      declarations: [ManagedAccountsCommentaryWrapperComponent],
      providers: [
        { provide: ToastrService, useValue: mockToastrService },
        { provide: ManagedAccountService, useValue: mockManagedAccountService },
        { provide: AccountService, useValue: { 
          getToken: jasmine.createSpy('getToken').and.returnValue('mock-token'), 
          redirectToUnauthorized: jasmine.createSpy('redirectToUnauthorized'), 
          addupdateSessionId: jasmine.createSpy('addupdateSessionId').and.returnValue(of({})) 
        } }
      ],
      schemas: [NO_ERRORS_SCHEMA]
    }).compileComponents();

    fixture = TestBed.createComponent(ManagedAccountsCommentaryWrapperComponent);
    component = fixture.componentInstance;

    // Set default input values
    component.managedAccountId = mockManagedAccountId;
    component.isLoading = false;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('ngOnInit', () => {
    it('should load commentaries', () => {
      spyOn(component, 'loadCommentaries');

      component.ngOnInit();

      expect(component.loadCommentaries).toHaveBeenCalled();
    });

    it('should initialize component properly', () => {
      spyOn(component, 'loadCommentaries');

      component.ngOnInit();

      expect(component.commentaryList).toBeDefined();
      expect(component.loadCommentaries).toHaveBeenCalled();
    });

    it('should update commentary names if subPagefieldList is available', () => {
      spyOn(component, 'loadCommentaries');
      spyOn(component as any, 'updateCommentaryNames');
      component.subPagefieldList = [{ name: 'test', displayName: 'Test' }];

      component.ngOnInit();

      expect((component as any).updateCommentaryNames).toHaveBeenCalled();
    });
  });

  describe('ngOnDestroy', () => {
    it('should unsubscribe from subscriptions', () => {
      spyOn(component['subscription'], 'unsubscribe');

      component.ngOnDestroy();

      expect(component['subscription'].unsubscribe).toHaveBeenCalled();
    });
  });

  describe('loadCommentaries', () => {
    beforeEach(() => {
      spyOn(component as any, 'setLoading');
    });

    it('should load commentaries successfully', () => {
      mockManagedAccountService.getCommentariesByManagedAccount.and.returnValue(of(mockCommentaryResponse));
      spyOn(component as any, 'processCommentaryResponse');

      component.loadCommentaries();

      expect((component as any).setLoading).toHaveBeenCalledWith(true);
      expect(mockManagedAccountService.getCommentariesByManagedAccount).toHaveBeenCalledWith(mockManagedAccountId);
      expect((component as any).processCommentaryResponse).toHaveBeenCalledWith(mockCommentaryResponse);
      expect((component as any).setLoading).toHaveBeenCalledWith(false);
    });

    it('should handle error when loading commentaries', () => {
      const mockError = new Error('Failed to load');
      mockManagedAccountService.getCommentariesByManagedAccount.and.returnValue(throwError(mockError));
      spyOn(console, 'error');

      component.loadCommentaries();

      expect((component as any).setLoading).toHaveBeenCalledWith(true);
      expect((component as any).setLoading).toHaveBeenCalledWith(false);
      expect(console.error).toHaveBeenCalledWith('Error loading commentaries:', mockError);
      expect(mockToastrService.error).toHaveBeenCalledWith(
        'Failed to load commentaries.',
        '',
        { positionClass: 'toast-center-center' }
      );
    });

    it('should show error when accountId is missing', () => {
      component.managedAccountId = null;

      component.loadCommentaries();

      expect((component as any).setLoading).toHaveBeenCalledWith(false);
      expect(mockToastrService.error).toHaveBeenCalledWith(
        'Account ID is required.',
        '',
        { positionClass: 'toast-center-center' }
      );
    });

    it('should show error when accountId is empty', () => {
      component.managedAccountId = '';

      component.loadCommentaries();

      expect((component as any).setLoading).toHaveBeenCalledWith(false);
      expect(mockToastrService.error).toHaveBeenCalledWith(
        'Account ID is required.',
        '',
        { positionClass: 'toast-center-center' }
      );
    });
  });

  describe('processCommentaryResponse', () => {
    beforeEach(() => {
      spyOn(component as any, 'isEmpty').and.returnValue(false);
    });

    it('should process commentary response correctly', () => {
      (component as any).processCommentaryResponse(mockCommentaryResponse);

      expect(component.commentaryList[0].newComment).toBe('<p>Test GLI Commentary</p>');
      expect(component.commentaryList[0].id).toBe(1);
      expect(component.commentaryList[1].newComment).toBe('<p>Test Market Commentary</p>');
      expect(component.commentaryList[1].id).toBe(2);
    });

    it('should auto-expand panels with content', () => {
      (component as any).isEmpty = jasmine.createSpy('isEmpty').and.returnValue(false);

      (component as any).processCommentaryResponse(mockCommentaryResponse);

      expect(component.commentaryList[0].isExpanded).toBe(true);
      expect(component.commentaryList[1].isExpanded).toBe(true);
    });

    it('should not expand panels without content', () => {
      (component as any).isEmpty = jasmine.createSpy('isEmpty').and.returnValue(true);

      (component as any).processCommentaryResponse([]);

      expect(component.commentaryList[0].isExpanded).toBe(false);
      expect(component.commentaryList[1].isExpanded).toBe(false);
    });

    it('should handle null response', () => {
      expect(() => (component as any).processCommentaryResponse(null)).not.toThrow();
    });

    it('should handle empty response array', () => {
      (component as any).processCommentaryResponse([]);

      expect(component.commentaryList[0].newComment).toBe('');
      expect(component.commentaryList[1].newComment).toBe('');
    });
  });

  describe('updateCommentaryFromResponse', () => {
    it('should update GLI commentary', () => {
      const responseData = {
        commentaryID: 1,
        commentaryText: '<p>Updated GLI</p>',
        commentaryType: 1
      };

      (component as any).updateCommentaryFromResponse(responseData);

      expect(component.commentaryList[0].newComment).toBe('<p>Updated GLI</p>');
      expect(component.commentaryList[0].id).toBe(1);
    });

    it('should update Market commentary', () => {
      const responseData = {
        commentaryID: 2,
        commentaryText: '<p>Updated Market</p>',
        commentaryType: 2
      };

      (component as any).updateCommentaryFromResponse(responseData);

      expect(component.commentaryList[1].newComment).toBe('<p>Updated Market</p>');
      expect(component.commentaryList[1].id).toBe(2);
    });

    it('should handle commentary without text', () => {
      const responseData = {
        commentaryID: 1,
        commentaryText: null,
        commentaryType: 1
      };

      expect(() => (component as any).updateCommentaryFromResponse(responseData)).not.toThrow();
    });

    it('should handle invalid commentary type', () => {
      const responseData = {
        commentaryID: 999,
        commentaryText: '<p>Invalid type</p>',
        commentaryType: 999
      };

      expect(() => (component as any).updateCommentaryFromResponse(responseData)).not.toThrow();
    });
  });

  describe('onCommentaryUpdated', () => {
    beforeEach(() => {
      spyOn(component as any, 'saveCommentary');
    });

    it('should update commentary and save', () => {
      const updatedCommentary = { ...mockCommentary, newComment: 'Updated content' };

      component.onCommentaryUpdated(updatedCommentary);

      expect(component.commentaryList[0].newComment).toBe('Updated content');
      expect((component as any).saveCommentary).toHaveBeenCalledWith(updatedCommentary);
    });

    it('should handle commentary not found in list', () => {
      const updatedCommentary = { ...mockCommentary, tableId: 999 };

      expect(() => component.onCommentaryUpdated(updatedCommentary)).not.toThrow();
      expect((component as any).saveCommentary).not.toHaveBeenCalled();
    });
  });

  describe('onToggleExpansion', () => {
    it('should expand target panel when collapsed', () => {
      component.commentaryList[0].isExpanded = false;
      component.commentaryList[1].isExpanded = true; // this should be closed
      const targetCommentary = { ...mockCommentary, tableId: 24 };

      component.onToggleExpansion(targetCommentary);

      expect(component.commentaryList[0].isExpanded).toBe(true); // toggled from false to true
      expect(component.commentaryList[1].isExpanded).toBe(false); // closed
    });
  });

  describe('onEditToggled', () => {
    it('should sync edit state', () => {
      const targetCommentary = { ...mockCommentary, tableId: 24, isEdit: true };

      component.onEditToggled(targetCommentary);

      expect(component.commentaryList[0].isEdit).toBe(true);
    });

    it('should handle commentary not found', () => {
      const targetCommentary = { ...mockCommentary, tableId: 999, isEdit: true };

      expect(() => component.onEditToggled(targetCommentary)).not.toThrow();
    });
  });

  describe('saveCommentary', () => {
    beforeEach(() => {
      spyOn(component as any, 'setLoading');
      spyOn(component, 'loadCommentaries');
    });

    it('should save commentary successfully', () => {
      const mockSaveResponse = {
        commentaryId: 1,
        message: 'Commentary saved successfully'
      };
      mockManagedAccountService.saveCommentaries.and.returnValue(of(mockSaveResponse));

      (component as any).saveCommentary(mockCommentary);

      expect((component as any).setLoading).toHaveBeenCalledWith(true);
      expect(mockManagedAccountService.saveCommentaries).toHaveBeenCalled();
      expect((component as any).setLoading).toHaveBeenCalledWith(false);
      expect(mockToastrService.success).toHaveBeenCalledWith(
        'Commentary saved successfully',
        '',
        { positionClass: 'toast-center-center' }
      );
      expect(component.loadCommentaries).toHaveBeenCalled();
    });

    it('should handle save error with message', () => {
      const mockError = {
        error: { message: 'Validation failed' },
        message: 'HTTP error'
      };
      mockManagedAccountService.saveCommentaries.and.returnValue(throwError(mockError));
      spyOn(console, 'error');

      (component as any).saveCommentary(mockCommentary);

      expect((component as any).setLoading).toHaveBeenCalledWith(true);
      expect((component as any).setLoading).toHaveBeenCalledWith(false);
      expect(console.error).toHaveBeenCalledWith('Error saving commentary:', mockError);
      expect(mockToastrService.error).toHaveBeenCalledWith(
        'Validation failed',
        '',
        { positionClass: 'toast-center-center' }
      );
    });

    it('should handle generic save error', () => {
      const mockError = { message: 'Network error' };
      mockManagedAccountService.saveCommentaries.and.returnValue(throwError(mockError));

      (component as any).saveCommentary(mockCommentary);

      expect(mockToastrService.error).toHaveBeenCalledWith(
        'Network error',
        '',
        { positionClass: 'toast-center-center' }
      );
    });

    it('should handle error without specific message', () => {
      const mockError = {};
      mockManagedAccountService.saveCommentaries.and.returnValue(throwError(mockError));

      (component as any).saveCommentary(mockCommentary);

      expect(mockToastrService.error).toHaveBeenCalledWith(
        'Failed to save commentary.',
        '',
        { positionClass: 'toast-center-center' }
      );
    });
  });

  describe('mapToCommentaryModel', () => {
    it('should map commentary to payload model', () => {
      const result = (component as any).mapToCommentaryModel(mockCommentary, mockManagedAccountId);

      expect(result).toEqual({
        commentaryID: 1,
        commentaryText: '<p>Test comment</p>',
        commentaryType: 1,
        managedAccountID: mockManagedAccountId
      });
    });

    it('should handle commentary without ID', () => {
      const commentaryWithoutId = { ...mockCommentary, id: null };
      const result = (component as any).mapToCommentaryModel(commentaryWithoutId, mockManagedAccountId);

      expect(result.commentaryID).toBe(0);
    });

    it('should handle empty comment text', () => {
      const commentaryWithEmptyText = { ...mockCommentary, newComment: '' };
      const result = (component as any).mapToCommentaryModel(commentaryWithEmptyText, mockManagedAccountId);

      expect(result.commentaryText).toBe('');
    });

    it('should handle null comment text', () => {
      const commentaryWithNullText = { ...mockCommentary, newComment: null };
      const result = (component as any).mapToCommentaryModel(commentaryWithNullText, mockManagedAccountId);

      expect(result.commentaryText).toBe('');
    });
  });

  describe('isEmpty', () => {
    it('should return true for undefined', () => {
      expect((component as any).isEmpty(undefined)).toBe(true);
    });

    it('should return true for null', () => {
      expect((component as any).isEmpty(null)).toBe(true);
    });

    it('should return true for empty string', () => {
      expect((component as any).isEmpty('')).toBe(true);
    });

    it('should return true for HTML with no text content', () => {
      expect((component as any).isEmpty('<p></p>')).toBe(true);
      expect((component as any).isEmpty('<div><span></span></div>')).toBe(true);
    });

    it('should return false for non-empty string', () => {
      expect((component as any).isEmpty('text')).toBe(false);
      expect((component as any).isEmpty('<p>content</p>')).toBe(false);
    });

    it('should handle complex HTML', () => {
      expect((component as any).isEmpty('<p><strong></strong></p>')).toBe(true);
      expect((component as any).isEmpty('<p><strong>content</strong></p>')).toBe(false);
    });
  });

  describe('setLoading', () => {
    beforeEach(() => {
      spyOn(component.loadingChange, 'emit');
    });

    it('should set loading state and emit event', () => {
      (component as any).setLoading(true);

      expect(component.isLoading).toBe(true);
      expect(component.loadingChange.emit).toHaveBeenCalledWith(true);
    });

    it('should unset loading state and emit event', () => {
      (component as any).setLoading(false);

      expect(component.isLoading).toBe(false);
      expect(component.loadingChange.emit).toHaveBeenCalledWith(false);
    });
  });

  describe('Edge Cases and Integration', () => {
    it('should handle rapid state changes', () => {
      (component as any).setLoading(true);
      (component as any).setLoading(false);
      (component as any).setLoading(true);

      expect(component.isLoading).toBe(true);
    });

    it('should handle multiple commentary updates', () => {
      spyOn(component as any, 'saveCommentary');

      const commentary1 = { ...mockCommentary, tableId: 24 };
      const commentary2 = { ...mockCommentary, tableId: 25 };

      component.onCommentaryUpdated(commentary1);
      component.onCommentaryUpdated(commentary2);

      expect((component as any).saveCommentary).toHaveBeenCalledTimes(2);
    });

    it('should maintain state consistency during error scenarios', () => {
      mockManagedAccountService.getCommentariesByManagedAccount.and.returnValue(throwError('Network error'));

      const initialState = [...component.commentaryList];
      component.loadCommentaries();

      // Commentary list should remain unchanged after error
      expect(component.commentaryList.length).toBe(initialState.length);
      expect(component.isLoading).toBe(false);
    });

    it('should handle component destruction during async operations', () => {
      mockManagedAccountService.getCommentariesByManagedAccount.and.returnValue(of(mockCommentaryResponse));

      component.loadCommentaries();
      component.ngOnDestroy();

      expect(component['subscription'].closed).toBeTruthy();
    });

    it('should handle large commentary text', () => {
      const largeText = 'a'.repeat(50000);
      const largeCommentary = { ...mockCommentary, newComment: largeText };

      const result = (component as any).mapToCommentaryModel(largeCommentary, mockManagedAccountId);

      expect(result.commentaryText).toBe(largeText);
    });

    it('should handle special characters in commentary', () => {
      const specialCharText = '!@#$%^&*()_+-={}[]|\\:";\'<>?,./ åäö 中文 🚀';
      const specialCommentary = { ...mockCommentary, newComment: specialCharText };

      const result = (component as any).mapToCommentaryModel(specialCommentary, mockManagedAccountId);

      expect(result.commentaryText).toBe(specialCharText);
    });
  });
});
