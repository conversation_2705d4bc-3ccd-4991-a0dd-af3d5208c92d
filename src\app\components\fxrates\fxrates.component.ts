import {
  ActionsEnum,
  FeaturesEnum,
  UserSubFeaturesEnum,
} from "../../services/permission.service";
import { Component, HostListener, OnInit, ViewChild } from "@angular/core";
import { MatMenuTrigger } from "@angular/material/menu";
import { CurrencyService } from "src/app/services/currency.service";

// Interface for quarterly data with different methodologies
export interface QuarterData {
  dailyAverage?: number;
  asOnStartDate?: number;
  asOnLatestDate?: number;
}

// Interface for period data
export interface PeriodData {
  [key: string]: QuarterData;
}

// Interface for data structure
export interface FxRateData {
  fromCurrencyCode: string;
  toCurrencyCode: string;
  periods: PeriodData;
}

@Component({
  selector: "app-fxrates",
  templateUrl: "./fxrates.component.html",
  styleUrls: ["./fxrates.component.scss"],
})
export class FxratesComponent implements OnInit {
  selectedFxPreference: any = null;
  feature: typeof FeaturesEnum = FeaturesEnum;
  subFeature: typeof UserSubFeaturesEnum = UserSubFeaturesEnum;
  actions: typeof ActionsEnum = ActionsEnum;
  isLoading:boolean = false;
  // Data arrays
  fxrateslist: FxRateData[] = [];
  fxrateslistResults: any[] = [];
  fxrateslistResultsClone: any[] = [];
  systemApiBulkUploadDataSet: any[] = [];
  fxratePeriods: string[] = [];
  // UI properties
  isEnableView: boolean = false;
  // Selected methodologies (multiple can be selected)
  selectedMethodologies: string[] = ['Daily Average'];
  
  // Filter properties
  uniqueFromCurrencies: Array<{text: string, value: string}> = [];
  uniqueToCurrencies: Array<{text: string, value: string}> = [];
  filteredFromCurrencies: Array<{text: string, value: string}> = [];
  filteredToCurrencies: Array<{text: string, value: string}> = [];
  selectedFromCurrencies: string[] = [];
  selectedToCurrencies: string[] = [];
  tempFromCurrencies: string[] = [];
  tempToCurrencies: string[] = [];
  fromCurrencySearchText: string = '';
  toCurrencySearchText: string = '';
  
  // MatMenuTrigger references
  @ViewChild('fromCurrencyMenuTrigger', { static: false }) fromCurrencyMenuTrigger: MatMenuTrigger;
  @ViewChild('toCurrencyMenuTrigger', { static: false }) toCurrencyMenuTrigger: MatMenuTrigger;
  
  // Select all state properties
  get isAllFromCurrenciesSelected(): boolean {
    return this.tempFromCurrencies.length > 0 && 
           this.tempFromCurrencies.length === this.uniqueFromCurrencies.length;
  }
  
  get isFromCurrenciesIndeterminate(): boolean {
    return this.tempFromCurrencies.length > 0 && 
           this.tempFromCurrencies.length < this.uniqueFromCurrencies.length;
  }
  
  get isAllToCurrenciesSelected(): boolean {
    return this.tempToCurrencies.length > 0 && 
           this.tempToCurrencies.length === this.uniqueToCurrencies.length;
  }
  
  get isToCurrenciesIndeterminate(): boolean {
    return this.tempToCurrencies.length > 0 && 
           this.tempToCurrencies.length < this.uniqueToCurrencies.length;
  }
  
  // Map display names to data property names
  methodologyToProperty = {
    'Daily average': 'dailyAverage',
    'As on latest date': 'asOnLatestDate',
    'As on day before start date': 'asOnStartDate',
    'Average': 'average'
  };
  /**
   * Create a safe field id for kendo column definitions to avoid issues with
   * period strings that contain dashes or other non-identifier characters.
   */
  sanitizeFieldId(period: string, methodology: string): string {
    const property = this.methodologyToProperty[methodology] || 'value';
    const safePeriod = String(period).replace(/[^a-zA-Z0-9_]/g, '_');
    return `col_${safePeriod}_${property}`;
  }

  /**
   * Format a Date-like value to YYYY-MM-DD (no timezone/time component)
   */
  private formatDateOnly(value: any): string | null {
    if (!value) return null;
    const d = value instanceof Date ? value : new Date(value);
    if (isNaN(d.getTime())) return null;
    const year = d.getFullYear();
    const month = (d.getMonth() + 1).toString().padStart(2, '0');
    const day = d.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  /**
   * Update results arrays
   */
  private updateResults(): void {
    // Build display rows with flattened dynamic fields for the grid
    const displayRows = this.fxrateslist.map(row => {
      const display: any = { ...row };
      this.fxratePeriods.forEach(period => {
        this.selectedMethodologies.forEach(methodology => {
          const propertyName = this.methodologyToProperty[methodology] || this.methodologyToProperty['Daily average'];
          const fieldName = this.sanitizeFieldId(period, methodology);
          const value = row?.periods?.[period]?.[propertyName];
          display[fieldName] = value ?? null;
        });
      });
      return display;
    });
    this.fxrateslistResults = displayRows;
    this.fxrateslistResultsClone = [...displayRows];
    
    // Update unique currency lists for filters
    this.updateUniqueCurrencies();
  }
  
  /**
   * Update unique currency lists for filters
   */
  private updateUniqueCurrencies(): void {
    // Extract unique from currencies
    const fromCurrencies = new Set<string>();
    this.fxrateslistResultsClone.forEach(item => {
      if (item.fromCurrencyCode) {
        fromCurrencies.add(item.fromCurrencyCode);
      }
    });
    
    // Extract unique to currencies
    const toCurrencies = new Set<string>();
    this.fxrateslistResultsClone.forEach(item => {
      if (item.toCurrencyCode) {
        toCurrencies.add(item.toCurrencyCode);
      }
    });
    
    // Convert to dropdown format
    this.uniqueFromCurrencies = Array.from(fromCurrencies)
      .sort()
      .map(currency => ({ text: currency, value: currency }));
      
    this.uniqueToCurrencies = Array.from(toCurrencies)
      .sort()
      .map(currency => ({ text: currency, value: currency }));
      
    // Initialize filtered lists
    this.filteredFromCurrencies = [...this.uniqueFromCurrencies];
    this.filteredToCurrencies = [...this.uniqueToCurrencies];
  }

  /**
   * Get value for a specific period and methodology
   * This makes the methodology linking explicit in the code
   */
  getValueForMethodology(rowData: FxRateData, periodKey: string, methodology: string,isTitle:boolean = false): number | undefined {
    // Get the property name for this methodology
    const propertyName = this.methodologyToProperty[methodology];
    
    // Return the value for this period and methodology
    let value = rowData?.periods?.[periodKey]?.[propertyName];
    if (value !== undefined && value !== null && !isTitle) {
      value = Number(Number(value).toFixed(4));
    }
    return value ?? 'NA';
  }
  // Reference to matMenuTrigger to open/close the popup menu
  @ViewChild('masterMenuTrigger', { static: false }) masterMenuTrigger: MatMenuTrigger;
  fxrateslistColumns = [];
  frozenCols: any = [
    { field: "fromCurrencyCode", header: "From Currency" },
    { field: "toCurrencyCode", header: "To Currency" },
  ];
  fxSource = [
    { source: 'Bulk Upload' },
    { source: 'System API' }
  ];
  selectedFxSource: string = 'System API';
  fxOptions: any;
  isDefault: boolean = true;
  isLoader:boolean = false;
  searchText: string = '';
  constructor(private currencyservice: CurrencyService) {}

  ngOnInit(): void {
    this.getFxRateFilterOptions(false);
    this.selectedFxSource = 'System API';
    this.isEnableView = true;
  }

  /**
   * Filter grid data based on search text
   */
  onFilter(value: string): void {
    if (!value) {
      // Apply only currency filters if they exist
      this.applyAllFilters();
      return;
    }
    
    const filterValue = value.toLowerCase();
    // First apply currency filters
    const currencyFilteredResults = this.applyCurrencyFilters(this.fxrateslistResultsClone);
    
    // Then apply text search filter
    this.fxrateslistResults = currencyFilteredResults.filter(item => {
      // Search in from/to currency codes
      if (item.fromCurrencyCode?.toLowerCase().includes(filterValue) ||
          item.toCurrencyCode?.toLowerCase().includes(filterValue)) {
        return true;
      }
      
      // Search in period data values
      if (item.periods) {
        return Object.keys(item.periods).some(periodKey => {
          const periodData = item.periods[periodKey];
          return Object.values(periodData).some(val => 
            val?.toString().toLowerCase().includes(filterValue)
          );
        });
      }
      
      return false;
    });
  }
  
  /**
   * Apply all active filters (currency and search)
   */
  private applyAllFilters(): void {
    // First apply currency filters
    this.fxrateslistResults = this.applyCurrencyFilters(this.fxrateslistResultsClone);
    
    // Then apply search filter if it exists
    if (this.searchText) {
      this.onFilter(this.searchText);
    }
  }
  
  /**
   * Apply only currency filters to the provided data
   */
  private applyCurrencyFilters(data: any[]): any[] {
    if ((!this.selectedFromCurrencies || this.selectedFromCurrencies.length === 0) && 
        (!this.selectedToCurrencies || this.selectedToCurrencies.length === 0)) {
      return [...data];
    }
    
    return data.filter(item => {
      // Check From Currency filter
      if (this.selectedFromCurrencies && this.selectedFromCurrencies.length > 0) {
        if (!this.selectedFromCurrencies.includes(item.fromCurrencyCode)) {
          return false;
        }
      }
      
      // Check To Currency filter
      if (this.selectedToCurrencies && this.selectedToCurrencies.length > 0) {
        if (!this.selectedToCurrencies.includes(item.toCurrencyCode)) {
          return false;
        }
      }
      
      return true;
    });
  }
  
  /**
   * Open From Currency filter menu
   */
  openFromCurrencyFilter(): void {
    // Copy current selection to temp variable
    this.tempFromCurrencies = [...this.selectedFromCurrencies];
    this.fromCurrencySearchText = '';
    this.filteredFromCurrencies = [...this.uniqueFromCurrencies];
    this.fromCurrencyMenuTrigger.openMenu();
  }
  
  /**
   * Apply From Currency filter
   */
  onApplyFromFilter(): void {
    // Update the selected currencies with the temp values
    this.selectedFromCurrencies = [...this.tempFromCurrencies];
    this.applyAllFilters();
    this.fromCurrencyMenuTrigger.closeMenu();
  }
  
  /**
   * Cancel From Currency filter
   */
  onCancelFromFilter(): void {
    // Reset temp values without applying
    this.tempFromCurrencies = [...this.selectedFromCurrencies];
    this.fromCurrencyMenuTrigger.closeMenu();
  }
  
  /**
   * Search in From Currency filter
   */
  onFromCurrencySearch(): void {
    if (!this.fromCurrencySearchText) {
      this.filteredFromCurrencies = [...this.uniqueFromCurrencies];
      return;
    }
    
    const searchText = this.fromCurrencySearchText.toLowerCase();
    this.filteredFromCurrencies = this.uniqueFromCurrencies.filter(
      currency => currency.text.toLowerCase().includes(searchText)
    );
  }
  
  /**
   * Check if a From Currency is selected
   */
  isFromCurrencySelected(value: string): boolean {
    return this.tempFromCurrencies.includes(value);
  }
  
  /**
   * Toggle a From Currency selection
   */
  toggleFromCurrency(value: string, event: any): void {
    const checked = event.checked;
    const index = this.tempFromCurrencies.indexOf(value);
    
    if (checked && index === -1) {
      this.tempFromCurrencies.push(value);
    } else if (!checked && index !== -1) {
      this.tempFromCurrencies.splice(index, 1);
    }
  }
  
  /**
   * Toggle all From Currencies
   */
  toggleAllFromCurrencies(event: any): void {
    if (event.checked) {
      this.tempFromCurrencies = this.uniqueFromCurrencies.map(c => c.value);
    } else {
      this.tempFromCurrencies = [];
    }
  }
  
  /**
   * Open To Currency filter menu
   */
  openToCurrencyFilter(): void {
    // Copy current selection to temp variable
    this.tempToCurrencies = [...this.selectedToCurrencies];
    this.toCurrencySearchText = '';
    this.filteredToCurrencies = [...this.uniqueToCurrencies];
    this.toCurrencyMenuTrigger.openMenu();
  }
  
  /**
   * Apply To Currency filter
   */
  onApplyToFilter(): void {
    // Update the selected currencies with the temp values
    this.selectedToCurrencies = [...this.tempToCurrencies];
    this.applyAllFilters();
    this.toCurrencyMenuTrigger.closeMenu();
  }
  
  /**
   * Cancel To Currency filter
   */
  onCancelToFilter(): void {
    // Reset temp values without applying
    this.tempToCurrencies = [...this.selectedToCurrencies];
    this.toCurrencyMenuTrigger.closeMenu();
  }
  
  /**
   * Search in To Currency filter
   */
  onToCurrencySearch(): void {
    if (!this.toCurrencySearchText) {
      this.filteredToCurrencies = [...this.uniqueToCurrencies];
      return;
    }
    
    const searchText = this.toCurrencySearchText.toLowerCase();
    this.filteredToCurrencies = this.uniqueToCurrencies.filter(
      currency => currency.text.toLowerCase().includes(searchText)
    );
  }
  
  /**
   * Check if a To Currency is selected
   */
  isToCurrencySelected(value: string): boolean {
    return this.tempToCurrencies.includes(value);
  }
  
  /**
   * Toggle a To Currency selection
   */
  toggleToCurrency(value: string, event: any): void {
    const checked = event.checked;
    const index = this.tempToCurrencies.indexOf(value);
    
    if (checked && index === -1) {
      this.tempToCurrencies.push(value);
    } else if (!checked && index !== -1) {
      this.tempToCurrencies.splice(index, 1);
    }
  }
  
  /**
   * Toggle all To Currencies
   */
  toggleAllToCurrencies(event: any): void {
    if (event.checked) {
      this.tempToCurrencies = this.uniqueToCurrencies.map(c => c.value);
    } else {
      this.tempToCurrencies = [];
    }
  }

  // Get the original API method (keeping for reference)
  getBulkuploadfxrates() {
    this.isLoader = true;
    let payload = {
      "fxRateSource": this.selectedFxSource ?? "System API",
      "fromDate": this.formatDateOnly(this.selectedFxPreference?.fromDate),
      "toDate": this.formatDateOnly(this.selectedFxPreference?.toDate),
      "currencyPair": this.selectedFxPreference?.fxRateCurrencyModels
        ?.flatMap((item: any) =>
          item.children && Array.isArray(item.children)
            ? item.children.map((child: any) => child.value)
            : []
        )?.join(",") ?? this.fxOptions?.fxRateCurrencyModels
        ?.flatMap((item: any) =>
          item.children && Array.isArray(item.children)
            ? item.children.map((child: any) => child.value)
            : []
        )?.join(",") ,
      "periodSource": this.selectedFxPreference?.period ?? "1 YR (Last 1 year)",
      "fyEnd": (this.selectedFxPreference?.financialYearEnd || [])
                  .map((item: any) => item?.fyEndName || item)
                  .join(",") || "December",
      "frequencies": this.selectedFxPreference?.frequency?.map((item: any) => item?.frequencyName || item)?.join(",") ?? "Quarterly",
      "methodologies": (this.selectedFxPreference?.fxMethodology || [])
                          .map((item: any) => item?.methodologyId || item)
                          .join(",") || "1"

    }
  
    this.currencyservice.GetFxratesByPreference(payload).subscribe((resp: any) => {
      this.isLoader = false;
      this.masterMenuTrigger.closeMenu();
      this.fxrateslist = [];
      const periods: string[] = Array.isArray(resp?.periods) ? resp.periods : [];
      const methodologies: string[] = Array.isArray(resp?.methodologies) ? resp.methodologies : [];
      this.fxratePeriods = periods;

      const mapMethodologyToProperty = (name: string): string | undefined => {
        if (!name) return undefined;
        const trimmed = String(name).trim();
        return this.methodologyToProperty[trimmed] || this.methodologyToProperty[trimmed.replace(/\s+/g, ' ')];
      };

      // Respect API methodologies if present (only keep recognized ones)
      if (methodologies.length) {
        const valid = methodologies.filter(m => !!mapMethodologyToProperty(m));
        if (valid.length) {
          this.selectedMethodologies = valid;
        }
      }

      // Build grid rows from groupedResult
      const grouped: any[] = Array.isArray(resp?.groupedResult) ? resp.groupedResult : [];
      this.fxrateslist = grouped.map(group => {
        const periodsData: PeriodData = {};
        periods.forEach(p => { periodsData[p] = {}; });
        const rateValues: any[] = Array.isArray(group?.rateValues) ? group.rateValues : [];
        rateValues.forEach(rv => {
          const periodKey: string = rv?.periodName;
          const propertyName = mapMethodologyToProperty(rv?.methodologyName);
          if (periodKey && propertyName) {
            if (!periodsData[periodKey]) periodsData[periodKey] = {};
            periodsData[periodKey][propertyName] = rv?.rate ?? undefined;
          }
        });
        const row: FxRateData = {
          fromCurrencyCode: group?.fromCurrencyCode,
          toCurrencyCode: group?.toCurrencyCode,
          periods: periodsData
        };
        return row;
      });

      this.updateResults();

      this.isEnableView = true;
    });
  }

  getFxRateFilterOptions(isBulkUpload: boolean) {
    this.isLoader = true;
    this.currencyservice.getFxRateFilterOptions(isBulkUpload).subscribe({
      next: (options) => {
        this.isLoader = false;
        this.fxOptions = options;
        // If API provides fxSource, use it, else keep default
        if (options.fxSource && Array.isArray(options.fxSource)) {
          this.fxSource = options.fxSource;
        }
        if(this.isDefault)
        {
          this.getBulkuploadfxrates();
        }
      },
      error: (error) => {
        // Handle error here
        this.isLoader = false;
        console.error(error);
      },
    });
  }

  onFxSourceChange(value: string) {
    this.isDefault = false;
    this.selectedFxSource = value;
    this.fxrateslist = [];
    this.fxrateslistResults = [];
    this.fxrateslistResultsClone = [];
    this.fxratePeriods = [];
    this.selectedMethodologies = [];
    this.fxOptions = null;
    this.isDefault = true;
    if (value === 'Bulk Upload') {
      this.getFxRateFilterOptions(true);
    } else if (value === 'System API') {
      this.getFxRateFilterOptions(false);
    }
  }

  // Handler for form submit from child
  onFxRatesFormSubmit(formData: any) {
    this.selectedFxPreference = formData;
    // Close the configuration menu immediately after Apply
    this.masterMenuTrigger?.closeMenu();
    this.getBulkuploadfxrates();
  }

  // Handler for form cancel from child
  onFxRatesFormCancel(closeMenu: boolean) {
    if(closeMenu) {
    this.masterMenuTrigger?.closeMenu();
  }
}

  /**
   * Main method to download FX rates
   */
  downloadFxRates() {
    this.isLoading = true;
    
    // Build the payload with all necessary data
    const payload = this.buildDownloadPayload();

    // Make the API call
    this.currencyservice.downloadFxRates(payload).subscribe({
      next: (response: any) => {
        this.isLoading = false;
        this.handleDownloadResponse(response);
      },
      error: (error) => {
        this.isLoading = false;
        console.error('Error downloading FX rates:', error);
      }
    });
  }

  /**
   * Builds the complete payload for FX rates download
   * Includes filter handling for currency pairs
   */
  private buildDownloadPayload(): any {
    // Create the base payload with common parameters
    const payload = {
      "fxRateSource": this.selectedFxSource ?? "System API",
      "fromDate": this.formatDateOnly(this.selectedFxPreference?.fromDate),
      "toDate": this.formatDateOnly(this.selectedFxPreference?.toDate),
      "periodSource": this.selectedFxPreference?.period ?? "1 YR (Last 1 year)",
      "fyEnd": (this.selectedFxPreference?.financialYearEnd || [])
                  .map((item: any) => item?.fyEndName || item)
                  .join(",") || "December",
      "frequencies": this.selectedFxPreference?.frequency?.map((item: any) => item?.frequencyName || item)?.join(",") ?? "Quarterly",
      "methodologies": (this.selectedFxPreference?.fxMethodology || [])
                          .map((item: any) => item?.methodologyId || item)
                          .join(",") || "1"
    };
    
    // Add currency pair parameter based on filters
    if (this.selectedFromCurrencies.length > 0 || this.selectedToCurrencies.length > 0) {
      // Get currency pairs based on selected filters
      const currencyPairs = this.getCurrencyPairsFromFilters();
      if (currencyPairs.length > 0) {
        payload["currencyPair"] = currencyPairs.join(",");
      }
    } else {
      // If no currency filters are selected, use the default currencyPair from preferences
      payload["currencyPair"] = this.getDefaultCurrencyPairs();
    }

    return payload;
  }

  /**
   * Generate currency pairs based on selected currency filters
   */
  private getCurrencyPairsFromFilters(): string[] {
    const currencyPairs: string[] = [];
    
    // If both from and to currencies are selected, create pairs from their combinations
    if (this.selectedFromCurrencies.length > 0 && this.selectedToCurrencies.length > 0) {
      this.selectedFromCurrencies.forEach(fromCurr => {
        this.selectedToCurrencies.forEach(toCurr => {
          currencyPairs.push(`${fromCurr}-${toCurr}`);
        });
      });
    } 
    // If only from currencies are selected, pair them with all available to currencies
    else if (this.selectedFromCurrencies.length > 0) {
      // Get all unique to currencies from the data
      const allToCurrencies = Array.from(new Set(
        this.fxrateslistResultsClone
          .filter(item => this.selectedFromCurrencies.includes(item.fromCurrencyCode))
          .map(item => item.toCurrencyCode)
      ));
      
      this.selectedFromCurrencies.forEach(fromCurr => {
        allToCurrencies.forEach(toCurr => {
          currencyPairs.push(`${fromCurr}-${toCurr}`);
        });
      });
    } 
    // If only to currencies are selected, pair them with all available from currencies
    else if (this.selectedToCurrencies.length > 0) {
      // Get all unique from currencies from the data
      const allFromCurrencies = Array.from(new Set(
        this.fxrateslistResultsClone
          .filter(item => this.selectedToCurrencies.includes(item.toCurrencyCode))
          .map(item => item.fromCurrencyCode)
      ));
      
      allFromCurrencies.forEach(fromCurr => {
        this.selectedToCurrencies.forEach(toCurr => {
          currencyPairs.push(`${fromCurr}-${toCurr}`);
        });
      });
    }
    
    return currencyPairs;
  }

  /**
   * Get default currency pairs from preferences
   */
  private getDefaultCurrencyPairs(): string {
    return this.selectedFxPreference?.fxRateCurrencyModels
      ?.flatMap((item: any) =>
        item.children && Array.isArray(item.children)
          ? item.children.map((child: any) => child.value)
          : []
      )?.join(",") ?? this.fxOptions?.fxRateCurrencyModels
      ?.flatMap((item: any) =>
        item.children && Array.isArray(item.children)
          ? item.children.map((child: any) => child.value)
          : []
      )?.join(",");
  }

  /**
   * Handle the download response by creating and triggering file download
   */
  private handleDownloadResponse(response: any): void {
    // Create a blob from the response
    const blob = new Blob([response], { type: 'application/octet-stream' });
    
    // Create a link element to trigger the download
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    
    // Set the filename - use current date if no specific name is available
    const currentDate = new Date().toISOString().split('T')[0];
    link.download = `FX_Rates_${currentDate}.xlsx`;
    
    // Append to the document, click it, and remove it
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // Clean up the URL object
    window.URL.revokeObjectURL(url);
  }
}
