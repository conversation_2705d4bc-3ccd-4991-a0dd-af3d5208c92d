import { ComponentFixture, TestBed } from '@angular/core/testing';
import { TemplateBuilderComponent } from './template-builder-component.component';
import { ReactiveFormsModule } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { InvestCompanyService } from '../../investmentcompany/investmentcompany.service';
import { of } from 'rxjs';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { KendoModule } from 'src/app/custom-modules/kendo.module';
import { TemplateBuilderComponentService } from '../../services/template-builder-component.service';
import { ToastrService } from 'ngx-toastr';

describe('TemplateBuilderComponent', () => {
  let component: TemplateBuilderComponent;
  let fixture: ComponentFixture<TemplateBuilderComponent>;
  let mockTemplateService: any;
  let mockToastr: any;
  let mockInvestCompanyService: any;
  let mockRouter: any;

  beforeEach(() => {
        mockInvestCompanyService = {
      getInvestCompanyListForClo: jasmine.createSpy('getInvestCompanyListForClo').and.returnValue(of([{ id: 1, name: 'TestCo' }]))
        };
    mockTemplateService = {
      saveNewsletterTemplate: jasmine.createSpy('saveNewsletterTemplate').and.returnValue(of({ status: true, message: 'Saved' })),
      getTableList: jasmine.createSpy('getTableList').and.returnValue(of([{ fields: [{ aliasName: 'Test', format: '', fontColor: '', bgColor: '', fontWeight: '', parent: '', fieldAlignments: '', position: null, size: null }] }])),
      getTemplateById: jasmine.createSpy('getTemplateById').and.returnValue(of({ reportTypeId: 2, companyId: 1, templateName: 'Test Template' })),
      checkTemplateNameExists: jasmine.createSpy('checkTemplateNameExists').and.returnValue(of(false))
    };
    mockToastr = { success: jasmine.createSpy('success'), error: jasmine.createSpy('error') };
       
    mockRouter = { navigate: jasmine.createSpy('navigate') };

    TestBed.configureTestingModule({
      declarations: [TemplateBuilderComponent],
      imports: [ReactiveFormsModule, BrowserAnimationsModule, KendoModule],
      providers: [
        { provide: TemplateBuilderComponentService, useValue: mockTemplateService },
        { provide: InvestCompanyService, useValue: mockInvestCompanyService },
        { provide: Router, useValue: mockRouter },
        { provide: ToastrService, useValue: mockToastr },
        { provide: ActivatedRoute, useValue: { snapshot: { params: {} } } }
      ],
      schemas: [NO_ERRORS_SCHEMA] 
    });

    fixture = TestBed.createComponent(TemplateBuilderComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize FilterForm with required controls', () => {
    expect(component.FilterForm.contains('selectedCompany')).toBeTrue();
    expect(component.FilterForm.contains('selectedReportType')).toBeTrue();
    expect(component.FilterForm.contains('templateName')).toBeTrue();
  });

  it('should fetch investment company list on init', () => {
    expect(mockInvestCompanyService.getInvestCompanyListForClo).toHaveBeenCalled();
    expect(component.cloCompanyList.length).toBeGreaterThan(0);
    expect(component.selectedCompany).toEqual({ id: 1, name: 'TestCo' });
  });

  it('should set selectedReportType when onSelectReportType is called', () => {
    component.onSelectReportType('CLO Page');
    expect(component.selectedReportType).toBe('CLO Page');
  });

  it('should navigate on cancel', () => {
    component.onCancel();
    expect(mockRouter.navigate).toHaveBeenCalledWith(['/clo-automated-newsletter']);
  });

  it('should set selectedCompany on company change', () => {
    component.onCompanyChange({ id: 2, name: 'OtherCo' });
    expect(component.selectedCompany).toEqual({ id: 2, name: 'OtherCo' });
  });

  it('should detect empty drop', () => {
    component.droppedItemsByPage = [[], []];
    expect(component.isDropEmpty()).toBeTrue();
    component.droppedItemsByPage = [[{ id: 1 }], []];
    expect(component.isDropEmpty()).toBeFalse();
  });

  it('should call saveNewsletterTemplate and show success on valid submit', () => {
    component.templateUniqueId = component.EMPTY_GUID;
    component.FilterForm.setValue({
      selectedCompany: { id: 1, name: 'TestCo' },
      selectedReportType: { id: 2, text: 'CLO Page' },
      templateName: 'My Template'
    });
    spyOn(component, 'getConfiguration');
    component.onSubmit();
    expect(mockTemplateService.saveNewsletterTemplate).toHaveBeenCalled();
    expect(mockToastr.success).toHaveBeenCalledWith('Saved', '', { positionClass: 'toast-center-center' });
    expect(component.getConfiguration).toHaveBeenCalledWith(2);
  });

  it('should not call saveNewsletterTemplate on invalid submit', () => {
    component.FilterForm.setValue({
      selectedCompany: null,
      selectedReportType: null,
      templateName: ''
    });
    spyOn(console, 'log');
    component.onSubmit();
    expect(mockTemplateService.saveNewsletterTemplate).not.toHaveBeenCalled();
    expect(console.log).toHaveBeenCalledWith('Form is invalid');
  });

  it('should populate itemTypes and filteredItemTypes in getConfiguration', () => {
    component.getConfiguration(1);
    expect(mockTemplateService.getTableList).toHaveBeenCalledWith(1, 0, 0);
    expect(component.itemTypes.length).toBeGreaterThan(0);
    expect(component.filteredItemTypes.length).toBeGreaterThan(0);
  });

  it('should patch FilterForm values in getTemplateById', () => {
    component.cloCompanyList = [{ id: 1, name: 'TestCo' }];
    component.reportTypes = [{ id: 2, text: 'CLO Page' }];
    component.getTemplateById();
    expect(component.FilterForm.value.selectedReportType).toEqual({ id: 2, text: 'CLO Page' });
    expect(component.FilterForm.value.selectedCompany).toEqual({ id: 1, name: 'TestCo' });
    expect(component.FilterForm.value.templateName).toBe('Test Template');
  });

  it('should filter items in filteredItems', () => {
    component.itemTypes = [
      { aliasName: 'Alpha' },
      { aliasName: 'Beta' },
      { aliasName: 'Gamma' }
    ];
    component.searchTerm = 'be';
    component.filteredItems();
    expect(component.filteredItemTypes).toEqual([{ aliasName: 'Beta' }]);
  });

  it('should reset filteredItemTypes when searchCancel is called', () => {
    component.itemTypes = [{ aliasName: 'Alpha' }];
    component.searchTerm = 'Alpha';
    component.filteredItems();
    expect(component.filteredItemTypes.length).toBe(1);
    component.searchCancel();
    expect(component.filteredItemTypes).toEqual(component.itemTypes);
    expect(component.searchable).toBeFalse();
    expect(component.searchTerm).toBeNull();
  });

  it('should add and remove pages correctly', () => {
    component.pageCount = 1;
    component.onPageCountChange(3);
    expect(component.pages.length).toBe(3);
    expect(component.droppedItemsByPage.length).toBe(3);
    component.onPageCountChange(2);
    expect(component.pages.length).toBe(2);
    expect(component.droppedItemsByPage.length).toBe(2);
  });

  it('should navigate pages with nextPage and prevPage', () => {
    component.pageCount = 3;
    component.currentPage = 0;
    component.nextPage();
    expect(component.currentPage).toBe(1);
    component.prevPage();
    expect(component.currentPage).toBe(0);
  });

  it('should slide pages correctly', () => {
    component.pages = [{}, {}, {}, {}, {}];
    component.slideStart = 0;
    component.slideWindowSize = 3;
    component.slideRight();
    expect(component.slideStart).toBe(1);
    component.slideLeft();
    expect(component.slideStart).toBe(0);
    component.setCurrentPage(4);
    expect(component.slideStart).toBe(2);
  });
})
