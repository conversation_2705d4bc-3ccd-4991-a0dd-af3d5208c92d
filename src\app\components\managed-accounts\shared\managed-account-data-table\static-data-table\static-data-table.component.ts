import { Component, Input, OnInit, OnChanges, SimpleChanges } from '@angular/core';
import { GetManagedCapTableValuesQuery } from '../../../models/managed-cap-table.model';
import { ManagedAccountService } from '../../../managed-account.service';
import { CellEditConstants } from 'src/app/common/constants';

@Component({
  selector: 'app-static-data-table',
  templateUrl: './static-data-table.component.html',
  styleUrls: ['./static-data-table.component.scss']
})
export class StaticDataTableComponent implements OnChanges {
  @Input() periodId: number = 0;
  @Input() isMonthly: boolean = false;
  @Input() isQuarterly: boolean = false;
  @Input() isAnnually: boolean = false;
  @Input() managedAccountId: string;
  @Input() moduleName: string;

  // Add any data properties needed for the static table
  tableColumns: any[] = [];
  tableResult: any[] = [];
  loading: boolean = false;
 
  constructor(private managedAccountService: ManagedAccountService) {}  


  ngOnChanges(changes: SimpleChanges): void {
    // Reload data when any of the period-related inputs change
    if (changes['periodId'] || changes['isMonthly'] || changes['isQuarterly'] || changes['isAnnually']) {
      this.loadStaticTableData();
    }
  }

  private loadStaticTableData(): void {
    // Only load data if we have a valid periodId
    if (this.periodId > 0) {
      this.loading = true;

      const query: GetManagedCapTableValuesQuery = {
        managedAccountId: this.managedAccountId,
        periodId: this.periodId,
        isMonthly: this.isMonthly,
        isQuarterly: this.isQuarterly,
        isAnnually: this.isAnnually,
        moduleName: this.moduleName
      };
  
    this.managedAccountService.getManagedCapTableValues(query).subscribe({
      next: (response) => {
        if (response && response.headers && response.rows) {
          this.tableResult = response.rows || [];
                    this.tableResult.forEach(kpiData => {
            const kpiInfoSpace = kpiData[CellEditConstants.KPIInfoSpace];
            if (kpiInfoSpace && kpiInfoSpace == CellEditConstants.KpiHashValue) {
              kpiData.KpiInfo = kpiData.KpiValue;
            } else if (kpiInfoSpace && kpiInfoSpace == CellEditConstants.KpiDollarValue) {
              kpiData.KpiInfo = CellEditConstants.KpiEuroValue + kpiData.KpiValue;
            }  else {
              kpiData.KpiInfo = kpiInfoSpace + (kpiData.KpiValue || '');
            }
          });
        } else {
          this.clearData();
        }
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading managed cap table values:', error);
        this.clearData();
        this.loading = false;
      }
    });
  }
}

  clearData(): void {
    this.loading = false;
    this.tableColumns = [];
    this.tableResult = [];
  }


}
