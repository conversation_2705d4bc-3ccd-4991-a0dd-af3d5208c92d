$nep-primary:#4061C7;
$nep-secondary:#1A237E;
$nep-base-grey:#FAFAFB;
$nep-text-grey:#55565A;
$nep-icon-grey:#75787B;
$nep-info:#00ACC1;
$nep-success:#388E3C;
$nep-error:#C62828;
$nep-warning:#C68700;
$nep-green:#4BD763;
$nep-orange: #FF9300;
$nep-purple:#5755D5;
$nep-blue:#5AC6F9;
$nep-black:#212121;
$nep-white: #FFFFFF;
$nep-white-secondary: #FFF;
$nep-dark-grey-title: #4D4D4D;
$nep-dark-grey-sub-h: #666666;
$nep-dark-blue-link: #4061C7;
$nep-dark-grey-sub-title: #808080;
$nep-dark-b-title: #E1E1E8;
$nep-light-bg: #FAFAFC;
$nep-light-grey-b: #E6E6E6;
$nep-light-grey-b: #E6E6E6;
$nep-link:#00568F;
$nep-text-color:#ABABAB;
$gray-100:#616161;
$gray-200:#7F7F7F;
$gray-300: #9E9E9E;
$gray-400:$nep-text-color;
$gray-500:#BDBDBD;
$gray-600:#CAC9C7;
$nep-divider:#DEDFE0;
$gray-800: #F0F0F1;
$gray-900: #F5F5F5;
$nep-text-color-dark:#212529;
$nep-deepskyblue:deepskyblue;
$nep-gray-dark:#b6babf;
$nep-light-bg:#fafafa;
$nep-dark-doc:#333333;
$nep-slate-grey:slategrey;
$nep-dark-link:#7e7d7d;
$nep-dark-blue:#0f5ae6;
$nep-dark-thick:#000;
$nep-dark-border:#d6d6d6;
$nep-light-border:#eee;
$nep-text-color-blue:#0028a0;
$nep-light-bg:#f2f2f2;
$nep-skyblue:#2399e5;
$nep-darker-blue:#031b87;
$nep-skyblue-light:#009fdf;
$nep-light-grey:#d5d6da;
$nep-dark-highlight:#186ba0;
$nep-darker-blue-color:#555555;
$nep-blue-border:#019add;
$nep-blue-600:#41b6e6;
$nep-grey-700:#d9d9d9;
$nep-blue-333:#333;
$nep-bg-light:#ebedf0;
$nep-highlighter:#B91F31;
$nep-shadow:#00000029;
$nep-dark-black:#000000;
$nep-dark-light:#50505C;
$nep-dark-h:#1A1A1A;
$nep-dark-bg:rgba(0, 0, 0, 0.25);
$nep-shadow-color:#00000014;
$nep-read-color:#DEDFD0;
$nep-light-yellow:#FFECB3;
$nep-light-h-bg:#EBF3FF;
$nep-font-medium:"Helvetica Neue LT W05_65 Medium",Arial, Verdana, Tahoma,sans-serif;

/*text */

$nep-title-color:$nep-black;
$nep-title-fontsize:24px;
$nep-title-line-height:30px;
$nep-subtitle-color:$nep-text-grey;
$nep-subtitle-fontsize:14px;
$nep-subtitle-line-height:22px;
$nep-body-color:$nep-text-grey;
$nep-body-fontsize:14px;
$nep-body-line-height:20px;
$nep-body-color:$nep-text-grey;
$nep-body-fontsize:14px;
$nep-body-line-height:20px;
$nep-button-color:$nep-text-grey;
$nep-button-fontsize:14px;
$nep-button-primary:#4061C7;
$nep-button-icon-primary:#3949AB;
$nep-button-disabled:#4061C7;
$nep-body-line-height:20px;
$nep-link-color:$nep-link;
$nep-link-fontsize:14px;
$nep-link-line-height:20px;
$nep-label-color:$nep-text-color;
$nep-label-fontsize:12px;
$nep-label-line-height:18px;
$nep-font-family: "Helvetica Neue LT W05_55 Roman",Arial, Verdana, Tahoma,sans-serif;
$nep-padding:16px;
$nep-row-active:#E8EAF6;
$nep-panel-color:#333;
$nep-panel-border:#DDD;
$nep-nav-tab:#495057;
$nep-nav-tab-border-color: #dee2e6;
$nep-fullwidth-selector:#ABABB8;
$font-medium:"Helvetica Neue LT W05_65 Medium",Arial, Verdana, Tahoma,sans-serif;
$nep-text-border-bottom:#b7b7b7;
$nep-text-border-color-focus: #ced4da;
$nep-text-placeholder-grey:#78757b;
$nep-button-hover-color:#F7F8FC;
$fs-panel-border: 1px solid #E6E6E6;


// color codes for New Dls
/*primary */
$Primary-110:#00072E;
$Primary-100:#021155;
$Primary-90:#152B7A;
$Primary-78:#4061C7;
$Primary-70:#5D7BC7;
$Primary-60:#93B0ED;
$Primary-50:#C4D9FF;
$Primary-47:#CFE0FF;
$Primary-43:#E3EEFF;
$Primary-40:#EBF3FF;
$Primary-35:#F5F9FF;

/*secondary */
$Secondary-130:#3A003D;
$Secondary-110:#9C27B0;
$Secondary-100:#BD00D6;
$Secondary-80:#E283FC;
$Secondary-70:#EAABFF;
$Secondary-60:#F9EBFF;

/*neutral gray */
$Neutral-Gray-100:#000000;
$Neutral-Gray-90:#1A1A1A;
$Neutral-Gray-70:#333333;
$Neutral-Gray-60:#4D4D4D;
$Neutral-Gray-30:#B3B3B3;
$Neutral-Gray-20:#CCCCCC;
$Neutral-Gray-10:#E6E6E6;
$Neutral-Gray-05:#F2F2F2;
$Neutral-Gray-02:#FAFAFA;
$Neutral-Gray-03:#F5F5F5;
$Neutral-Gray-00:#FFFFFF;
$Neutral-Gray-80:#666666;   

/*positive */
$Positive-120:#003B15;
$Positive-110:#056122;
$Positive-100:#1B873A;
$Positive-80:#6AD481;
$Positive-70:#A2FAB2;
$Positive-60:#CCFFD4;
$Positive-50:#EDFFF0;

/*negative */
$Negative-120:#910000;
$Negative-110:#B80D10;
$Negative-100:#DE3139;
$Negative-90:#FF5E6C;
$Negative-80:#F0808D;
$Negative-70:#FFABB7;
$Negative-60:#FFD1DA;
$Negative-50:#FFEDF0;

/*noticebale */
$Noticeable-110:#CF6700;
$Noticeable-100:#F68523;
$Noticeable-90:#FF984A;
$Noticeable-70:#FFBD96;
$Noticeable-60:#FFD3BD;
$Noticeable-50:#FFEBE3;

/*Informational */
$Informational-110:#0045D9;
$Informational-100:#1A6AFF;
$Informational-90:#4089FF;
$Informational-70:#8CC0FF;
$Informational-60:#B3D7FF;
$Informational-50:#D9EDFF;

/*Deep Blue */
$DeepBlue-120:#00072E;
$DeepBlue-110:#021154;
$DeepBlue-100:#152B7A;
$DeepBlue-90:#334FA1;
$DeepBlue-70:#5D7BC7;
$DeepBlue-60:#93B0ED;

/*Clear Blue */
$ClearBlue-130:#002C78;
$ClearBlue-120:#003F9E;
$ClearBlue-110:#0457C4;
$ClearBlue-100:#2881EA;
$ClearBlue-90:#52A8FF;
$ClearBlue-70:#78C0FF;
$ClearBlue-60:#9ED5FF;

/*Teal */
$Teal-130:#00242B;
$Teal-120:#004752;
$Teal-110:#006C78;
$Teal-100:#13969F;
$Teal-90:#35C0C4;
$Teal-70:#63EBEB;
$Teal-60:#91FFFB;

/*Turquoise */
$Turquoise-120:#10577D;
$Turquoise-110:#2E7EA3;
$Turquoise-100:#57A9C9;
$Turquoise-90:#8AD6EF;
$Turquoise-70:#BAF0FF;
$Turquoise-60:#E0F9FF;

/*Soft green */
$SoftGreen-130:#054F39;
$SoftGreen-120:#197556;
$SoftGreen-110:#389C77;
$SoftGreen-100:#63C19C;
$SoftGreen-90:#99E8C6;
$SoftGreen-70:#CFFFE8;


/*Salad green */
$SaladGreen-130:#286300;
$SaladGreen-120:#438A0C;
$SaladGreen-110:#69B02A;
$SaladGreen-100:#94D553;
$SaladGreen-90:#C6FC88;
$SaladGreen-70:#DDFFB0;
$SaladGreen-60:#EFFFD6;

/*Pale Yellow */
$PaleYellow-130:#7A7402;
$PaleYellow-120:#A1961B;
$PaleYellow-110:#C7B740;
$PaleYellow-100:#EDDA70;
$PaleYellow-90:#FFED9E;
$PaleYellow-60:#FFF2C4;
$PaleYellow-40:#FFFAEB;

/*Pale Yellow */
$PastelOrange-130:#754800;
$PastelOrange-120:#9C5B00;
$PastelOrange-110:#C2781D;
$PastelOrange-100:#E89A46;
$PastelOrange-90:#FFB773;
$PastelOrange-70:#FFC799;
$PastelOrange-60:#FFDABF;

/*Pale Red */
$Red-80:#D50000;

/* gradient */
$Brand-Gradient:linear-gradient(180deg, #021155 0%, #9C27B0 100%);
$Yellow-Gradient:linear-gradient(270deg, #EDCF28 0%, #DEAD09 100%);
$Teal-Gradient:linear-gradient(90deg, #67CCD9 0%, #A9C97F 100%);
$Blue-Gradient:linear-gradient(90deg, #2F85E9 0%, #65CBD9 100%);
$Orange-Gradient:linear-gradient(90deg, #EB7600 0%, #F8BE63 100%);

/* radius */
$Radius-12:12px;
$Radius-10:10px;
$Radius-4:4px;
$Radius-8:8px;
$Radius-28:28px;
$Radius-22:22px;
$Radius-20:20px;

/*spacing */
$Spacing-8:8px;
$Spacing-12:12px;

/* shadow */
$shadow-short:0px 0px 8px 0px #0000001F;
$shadow-long:0px 0px 4px 0px #00000014;

/*content */
$content-paragraph:#333333;
/* muted text color */
$content-muted:#6c757d;


$fs-panel-border: 1px solid #E6E6E6;

$popup-text-line-height: 20px;
$review-page-custom-fixedheight-padding: 10px;
$review-page-form-container-border: 1px;
$review-page-form-container-border-radius: 8px;
$review-page-max-height: 544px;
$company-content-border-bottom: 1px solid #F2F2F2;
$review-page-scroll-width: 8px;
$review-page-scroll-color: #F2F2F2;

$header-color: #4061C7;
$content-value-color: #000000;
$content-label-color: #666666;
$content-padding-6: 6px;
$content-padding-4: 4px;

$cell-bottom-border-blue: 1px solid #4061C7;
$default-cell-bottom-border: 1px solid #E6E6E6;
$cell-bottom-border-white: 1px solid #fff;
$cell-background-color: #EBF3FF;

// Colors & Sizes - edit-cell-dialog.component.scss
$primary-color: #4061c7;
$secondary-color-dg: #333333;
$tertiary-color-lg: #666666;
$placeholder-color: #b3b3b3;
$background-cell-color: #ebf3ff;
$neutral-gray: #e6e6e6;
$uploading-bg-color: #f5f9ff;
$bg-color-white: #fff;
$invalid-file-color: #FF0000;
$text-black: #000000;

$font-size-medium: 12px;
$margin-medium: 10px;
$border-radius: 4px;
$font-size-small: 11px;
$margin-large: 11px;
$padding-medium: 8px;
$padding-large: 16px;
$padding-small: 6px;
$margin-small: 8px;
$progressbar-right-padding: 25px;
$margin-top-label: 4px;
$file-margin-tp: 45px;
$progress-bar-height: 10px;
$vector-icon-height: 14px;
$vector-icon-width: 14px;
$current-value-border-radius: 100px;
$current-value-padding: 10px 20px;
$current-value-height: 20px;
$current-value-line-height: 12px;
$file-name-width: 95px;

$border-neutral-grey: 1px solid  #e6e6e6;
$border-primary: 1px solid #4061c7;
$border-primary-2: 2px solid #4061c7;
$clo-summary-table-padding: 20px; 
$clo-content-cell-height: 40px;

$delete-on-hover-color: #F5F9FF;
$delete-on-click-color: #EBF3FF;
$icon-size: 32px;
$compnay-cell-height: 40px;

$padding-25: 25px;
$padding-22: 22px;
$padding-21: 21px;
$padding-21_5: 21.5px;
$padding-16: 16px;
$padding-12: 12px;
$padding-6: 6px;
$margin-60: 60px;
$border-shadow-black: 0px 8px 12px 6px #0000000D;

// page configuration css variables
$space-4: 4px;
$space-8: 8px;
$space-12: 12px;
$space-20: 20px;
$border-color-light: 1px solid #F2F2F2;
$border-color-dark: 1px solid #E6E6E6;
$gray-color-66: #666666;
$dark-gray-color: #333333;
$pastel-blue-color: #93B0ED;
$light-blue-color: #F5F9FF;
$light-pastel-blue-color: #EBF3FF;
$semi-transparent-black-color: #0000001F;
$primary-color-78: #4061C7;
$white-color: #FFFFFF;
$dropdown-width: 217px;
$border-color-light-gray:1px solid #DEDFE0;
$neutral-divider-primary:#E6E6E6;

// Upload modal specific colors
$upload-divider-color: #e0e0e0;
$upload-error-border: #dc3545;
$upload-error-bg: #f8d7da;
$upload-success-border: #28a745;
$upload-success-bg: #d4edda;