<div class="track-record-container">
  <div class="track-record-content">
    <kendo-tabstrip id="{{selectedTab}}" (tabSelect)="selectedTabData($event)">
      <ng-container *ngFor="let tab of navigationTabLinks">
        <kendo-tabstrip-tab *ngIf="permissions[tab.tableName]?.canView" [title]="tab.aliasName"
          [selected]="navigationTabLinks[0] === tab">
          <ng-template kendoTabContent>
            <app-managed-account-data-table 
              [managedAccountId]="managedAccountId" 
              [tableTitle]='tab.aliasName'
              [moduleName]="tab.name" 
              [tableName]='tab.tableName' 
              [permissions]="permissions"
              [isStaticTable]="tab.isStaticTable">
            </app-managed-account-data-table>
          </ng-template>
        </kendo-tabstrip-tab>
      </ng-container>
    </kendo-tabstrip>
  </div>
</div>