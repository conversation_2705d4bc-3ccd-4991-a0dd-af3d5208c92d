<div class="managed-cap-table-container">
  <!-- Loading indicator -->
  <div class="cap-table-grid-container">
    <div *ngIf="loading" class="loading-container">
      <div class="spinner-border" role="status">
        <span class="sr-only">Loading...</span>
      </div>
    </div>

    <!-- Cap Table Grid -->
    <div *ngIf="!loading">
      <kendo-grid
        class="k-grid-border-right-width k-grid-border-bottom-width k-grid-outline-none custom-kendo-cap-table-grid"
        [kendoGridBinding]="tableResult" scrollable="virtual" [rowHeight]="40" [resizable]="true">

        <!-- Sticky first column for KPI/Instrument names -->
        <kendo-grid-column *ngIf="tableResult.length > 0" [sticky]="true" [minResizableWidth]="200"
          [maxResizableWidth]="800" [width]="400" field="Kpi">

          <ng-template kendoGridHeaderTemplate>
            <div *ngIf="tableColumns.length > 0" class="header-icon-wrapper wd-100">
              <span class="TextTruncate S-M">
                Instrument
              </span>
            </div>
          </ng-template>

          <ng-template kendoGridCellTemplate let-rowData>
            <div class="content">
              <span
                [ngClass]="[(rowData.IsHeader||rowData.IsBoldKpi) ? 'showToolTip TextTruncate bold-text' :'showToolTip TextTruncate',rowData.IsHeader ? 'headerKpi bold-text' : rowData['IsBoldKpi'] ? 'bold-text': '',((rowData.ParentKpiId !==0||rowData.ParentKpiId ==0)&&!rowData.IsHeader)?'pl-3':'']">
                <span *ngIf="rowData.ParentKpiId !== 0">- </span>{{rowData["Kpi"]}}
                <span *ngIf="rowData['KPI Info'] =='#'">{{'('+rowData['KPI Info'] +')'}}</span>
              </span>
            </div>
          </ng-template>
        </kendo-grid-column>

        <!-- Dynamic columns based on response headers -->
        <kendo-grid-column [minResizableWidth]="200" *ngFor="let col of tableColumns; index as i"
          [maxResizableWidth]="800" [width]="200" title="{{col.header}}">

          <ng-template kendoGridHeaderTemplate>
            <div class="header-icon-wrapper wd-100">
              <span class="TextTruncate S-M" *ngIf="col.kpiInfo==null">
                {{col.header}}
              </span>
              <span title="{{col.header}}{{'('+col['kpiInfo'] +')'}}" class="TextTruncate S-M"
                *ngIf="col.kpiInfo!=null && col.kpiInfo =='#'">
                {{col.header}} {{'('+col['kpiInfo'] +')'}}
              </span>
              <span title="{{col.header}}" class="TextTruncate S-M" *ngIf="col.kpiInfo!=null && col.kpiInfo !='#'">
                {{col.header}}
              </span>
            </div>
          </ng-template>

          <ng-template kendoGridCellTemplate let-rowData>
            <div class="content">
              <div *ngIf="col.header !='Kpi'"
                [ngClass]="[col['isBoldKpi'] == true && col['kpiInfo']!=null ? 'bold-text': '',  rowData.IsBoldKpi && col['kpiInfo']==null ? 'bold-text': '']"
                class="showToolTip TextTruncate" [class.table-data-right]="col.field !='KPI'">

                <div [title]="rowData['KPI Info']=='Text'? rowData[col.field]:''"
                  *ngIf="rowData[col.field]!= undefined && rowData[col.field]!=null && rowData[col.field]!=''&& !rowData.IsHeader;else empty_Text">

                  <ng-container
                    [ngSwitch]="col['kpiInfo']!=null && !rowData['IsOverrule'] ? col['kpiInfo'] :rowData['KPI Info']">

                    <!-- Number formatting -->
                    <container *ngSwitchCase="kpiInfo.Number">
                      <span class="float-right TextTruncate w100Percent" [title]="rowData[col.field]"
                        [innerHtml]="isNumberCheck(rowData[col.field]) ? (rowData[col.field] | number:'1.0-0' | minusSignToBrackets) : rowData[col.field]">
                      </span>
                    </container>

                    <!-- Text formatting -->
                    <container *ngSwitchCase="kpiInfo.Text">
                      <span class="float-left left-align TextTruncate w100Percent drop-above"
                        [title]="rowData[col.field]" [innerHtml]="rowData[col.field]">
                      </span>
                    </container>

                  <!-- Percentage formatting -->
                  <container *ngSwitchCase="kpiInfo.Percentage">
                    <span class="float-right TextTruncate w100Percent"
                          [title]="rowData[col.field]"
                          [innerHtml]="isNumberCheck(rowData[col.field]) ? (rowData[col.field] | number: NumberDecimalConst.doubleDecimal | minusToBracketsWithPercentage: '%'): rowData[col.field]">
                    </span>
                  </container>

                    <!-- Multiple formatting -->
                    <container *ngSwitchCase="kpiInfo.Multiple">
                      <span class="float-right TextTruncate w100Percent" [title]="rowData[col.field]"
                        *ngIf="rowData[col.field] != 'NA' && rowData[col.field] != ''"
                        [innerHtml]="isNumberCheck(rowData[col.field]) ? (rowData[col.field] | number: NumberDecimalConst.multipleDecimal)+'x': rowData[col.field]">
                      </span>
                    </container>

                    <!-- Currency formatting -->
                    <container *ngSwitchCase="kpiInfo.Currency">
                      <span class="float-right TextTruncate w100Percent" [title]="rowData[col.field]"
                        *ngIf="rowData[col.field] != 'NA' && rowData[col.field] != ''"
                        [innerHtml]="isNumberCheck(rowData[col.field]) ? (rowData[col.field] | number: NumberDecimalConst.currencyDecimal | minusSignToBrackets: '') : rowData[col.field]">
                      </span>
                    </container>

                    <!-- Default case -->
                    <container *ngSwitchDefault>
                    </container>

                  </ng-container>
                </div>

                <!-- Empty text template -->
                <ng-template #empty_Text class="detail-sec">
                  <div
                    [ngClass]="(col['kpiInfo']!=null && !rowData['IsOverrule'] ? col['kpiInfo'] :rowData['KPI Info'])=='Text'? 'float-left':'float-right'"
                    *ngIf="!rowData.IsHeader">NA</div>
                  <div *ngIf="rowData.IsHeader">
                    <div>&nbsp;</div>
                  </div>
                </ng-template>
              </div>
            </div>
          </ng-template>
        </kendo-grid-column>

        <!-- No records template -->
        <ng-template kendoGridNoRecordsTemplate>
          <div class="empty-state" *ngIf="tableResult.length == 0">
            <p>No data available</p>
          </div>
        </ng-template>

      </kendo-grid>
    </div>
  </div>
</div>