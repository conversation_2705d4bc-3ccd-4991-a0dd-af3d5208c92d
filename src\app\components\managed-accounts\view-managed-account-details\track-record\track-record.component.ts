import { Component, Input, OnChanges, SimpleChanges } from '@angular/core';
import { INavigationTabLink } from '../../shared/navigationLink.model';
import { ActivatedRoute, Router } from '@angular/router';
import { ManagedAccountService } from '../../managed-account.service';
import { ToastrService } from 'ngx-toastr';
import { ManagedAccount } from '../../managed-account.model';
import { ManagedAccountConstants, ManagedAccountPermissionConstants } from 'src/app/common/constants';

@Component({
  selector: 'app-track-record',
  templateUrl: './track-record.component.html',
  styleUrls: ['./track-record.component.scss']
})
export class TrackRecordComponent implements OnChanges {
  @Input() permissions: any; 
  @Input() subPagefieldList: any[];
  @Input() managedAccountId: string;

  
  isLoading: boolean = true;
  accountData: ManagedAccount;
  ManagedAccountConstant = ManagedAccountConstants;
  selectedTab: string = this.ManagedAccountConstant.TAB_NAMES.Track_Record;

  public navigationTabLinks: INavigationTabLink[] = [];

  constructor() {}

  ngOnChanges(changes: SimpleChanges) {
    if (changes['subPagefieldList'] && changes['subPagefieldList'].currentValue) {
      this.buildNavigationTabLinks();
    }
  }

  ngOnInit() {
    this.isLoading = false;
      if (this.subPagefieldList && this.subPagefieldList.length > 0) {
      this.buildNavigationTabLinks();
    }
    const firstVisibleTab = this.navigationTabLinks.find(tab => this.permissions?.[tab.name]?.canView);
    if (firstVisibleTab) {
      this.selectedTab = firstVisibleTab.name;
      this.navigationTabLinks.forEach(navTab => {
        navTab.isSelected = navTab.name === firstVisibleTab.name;
      });
    } else {
      this.selectedTab = null;
      this.navigationTabLinks.forEach(navTab => navTab.isSelected = false);
    }
  } 

  selectedTabData(tab: INavigationTabLink) {
    this.selectedTab = tab.name;
    this.navigationTabLinks.forEach(navTab => {
      navTab.isSelected = navTab.name === tab.name;
    });
  }

  private buildNavigationTabLinks() {
    if (this.subPagefieldList && this.subPagefieldList.length > 0) {      
      const trackRecordTabs = [ManagedAccountPermissionConstants.ManagedAccountSubFeature.PerformanceAttribution, ManagedAccountPermissionConstants.ManagedAccountSubFeature.TrackRecord];
      this.navigationTabLinks = this.subPagefieldList
        .filter(item => trackRecordTabs.some(tabName => item.name.includes(tabName) || item.name === tabName))
        .map((item, index) => ({
          name: item.name,
          aliasName: item.displayName || item.aliasName,
          tableName: item.name,
          isSelected: index === 0,
          isStaticTable: false
        }));
      }
  }

}
