<div *ngIf="permissions?.[ManagedAccountPermissionConstants.ManagedAccountSubFeature.InvestmentPortfolio]?.canView" class="clo-table-body col-12 col-lg-12 col-xl-12 col-sm-12 col-lg-12 col-md-12 pr-4 pl-4 pt-4 Heading2-M">
    <div class="company-facts-staticDataContainer investment-summary-no-borders">
        <div class="company-facts-glo-container" style="margin-top: 0px;">
            <app-managed-account-data-table 
                [managedAccountId]="managedAccountId"
                [tableTitle]='investmentPortfolioTitle'
                [moduleName]="ManagedAccountPermissionConstants.ManagedAccountSubFeature.InvestmentPortfolio"
                [tableName]='ManagedAccountPermissionConstants.ManagedAccountSubFeature.InvestmentPortfolio'
                [permissions]="permissions"
                [isStaticTable]="false">
        </app-managed-account-data-table>
        </div>
    </div>
</div>