<div class=" company-kpis" id="company-kpi">
  <div class="row mr-0 ml-0">
    <div class="col-lg-12 col-xl-12 col-12 col-sm-12 col-md-12 col-xl-12 pl-0 pr-0 backgroundColor">
            <div class="panel panel-default border-0 pt-0 tab-bg custom-panel">
                <div class="pull-right headerSize">
                    <div class="d-inline  QMY_Container" id="company-kpi-period-options">
                        <div class="d-inline custom-padding">
                            <div id="company-kpi-{{opt.field}}" class="d-inline QMY_Text MStyle QMYStyle" *ngFor="let opt of filterOptions" (click)="onChangePeriodOption(opt)" [ngClass]="opt.key ?'activeQMY':''">
                                {{opt.field}}
                            </div>
                        </div>
                    </div>
                </div>
                    <div class="panel-title custom-tabs">
                        <div class="float-left">
                            <div class="pl-3" >
                                <div class="nep-tabs nep-tabs-line">
                                    <div class="nep-tabs-header">
                                        <div class="nep-tabs-header-tabs">
                                            <div class="nep-tabs-inner">
                                                <div class="nep-tabs-scroll nep-tab-alignment-subtab financial-section" id="company-kpi-value-types">
                                                    <div class="nep-tabs-tab"  id="company-kpi-types" *ngFor="let tab of tabValueTypeList" (click)="selectValueTab(tab)" [class.nep-tabs-active]="tab.active" [ngStyle]="{'padding': '0px !important'}" id="{{tab.name == valueTypeIc ? '' : tab.name}}">
                                                        {{tab.name == valueTypeIc ? '' : tab.name}}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>   
                </div>
    </div>
</div>

<div class="row mr-0 ml-0 topBorder">
    <div class="col-lg-12 col-12 col-sm-12 col-md-12 col-xl-12 pl-0 pr-3 pl-3" id="company-kpi-graph-conatiner">
        <app-company-kpi-graph id="company-kpi-graph" [searchFilter]="searchFilter" [isValueUpdated]="isValueUpdated" [modelList]="model" [typeField]="defaultType" *ngIf="companyKpiConfigData.hasChart">
        </app-company-kpi-graph>
    </div>
</div>

<div class="portfolio-company-table">
    <div class="border-top filter-bg border-bottom" [ngClass]="tableResult != null && tableResult.length == 0 ?'pc-border-bottom':''">
        <div class="row mr-0 ml-0">
            <div class=" col-lg-12 col-md-12 col-sm-12 pl-0 pr-0">
                <div class="allvalues-kpis" id="company-kpi-allvalues">
                    <span id="company-kpi-default-value">Default: {{!isTaabo? model.reportingCurrencyDetail.currencyCode:''}} ({{companyKpiValueUnit?.unitType}})</span> 
                    <span id="company-kpi-converted-value" *ngIf="kpiCurrencyFilterModel?.currencyCode!=null && kpiCurrencyFilterModel?.isApply"> | Converted: {{kpiCurrencyFilterModel?.currencyCode}} ({{companyKpiValueUnit?.unitType}})</span> 
                    <span id="company-kpi-spotrate-value" *ngIf="kpiCurrencyFilterModel?.spotRate!=null && kpiCurrencyFilterModel?.isSpotRate && kpiCurrencyFilterModel?.isApply"> | Spot Rate: {{kpiCurrencyFilterModel?.currencyCode}} {{kpiCurrencyFilterModel?.spotRate}}</span>
                </div>
                <div class="pull-right headerfontsize">
                    <div class="d-inline-block search input-with-icon">
                        <span class="fa fa-search fasearchicon search-icon"></span>
                        <input #gb pInputText [appApplyFilter]="{ data: tableResultClone, columns: tableColumns,IsFreezeColumn:true,freezeColumns:'KPI'}"
                        (filtered)="tableResult = $event" type="text" class="search-text-company k-pl" placeholder="Search" [(ngModel)]="globalFilter" id="company-kpi-search">
                    </div>
                    <div class="d-inline-block pr-1" [hideIfUserUnAuthorized]='{subFeatureId:subFeature.CompanyKPIs,action:actions[actions.canEdit],id:id}'>
                        <div class="d-inline-block table-pref">Logs</div>
                        <div class="d-inline-block pr-2 pl-1" [title]="auditLogTitle">
                            <kendo-switch id="company-kpi-log" size="small" [(ngModel)]="isToggleChecked" (valueChange)="handleChange($event)" [onLabel]="' '" [offLabel]="' '"></kendo-switch>
                        </div>
                    </div>
                    <div class="d-inline textsplit" [hideIfUserUnAuthorized]='{subFeatureId:subFeature.CompanyKPIs,action:actions[actions.canEdit],id:id}'></div>
                    <div class="d-inline-block cloud_download">
                        <div class="d-inline-block pr-2" [hideIfUserUnAuthorized]='{subFeatureId:subFeature.CompanyKPIs,action:actions[actions.canExport],id:id}' *ngIf="tableResult != null && tableResult.length > 0"><img src="assets/dist/images/Cloud-download.svg" class="cursor-filter" title="Export KPI (Excel file)" id="company-kpi-download" alt="" (click)="exportCompanyKpiValues()" /><span class="excel-load" *ngIf="exportCompanyKPILoading">
                            <i aria-hidden="true" class="fa fa-spinner fa-pulse fa-1x fa-fw"></i>
                         </span></div>
                        <div class="d-inline textsplit" [hideIfUserUnAuthorized]='{subFeatureId:subFeature.CompanyKPIs,action:actions[actions.canExport],id:id}' *ngIf="tableResult != null && tableResult.length > 0"></div>

                        <div class="d-inline-block pl-2 pr-1"><img id="dropdownMenuButton" [matMenuTriggerFor]="menu" src="assets/dist/images/ConfigurationWhite.svg" class="cursor-filter" alt="" #companyMenuTrigger="matMenuTrigger" /> 
                            <span [matMenuTriggerFor]="menu" #filterMenuTrigger="matMenuTrigger" class="api-spot-rate-circle Caption-R" title="Spot Rate applied" *ngIf="kpiCurrencyFilterModel != null && kpiCurrencyFilterModel?.spotRate!=null && kpiCurrencyFilterModel?.isSpotRate && kpiCurrencyFilterModel?.isApply">S</span>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
    <div class="align-items-start" id="company-kpi-table">
        <kendo-grid id="kpi-grid" class="k-grid-border-right-width k-grid-border-bottom-width k-grid-outline-none custom-kendo-cab-table-grid" 
            [kendoGridBinding]="tableResult" scrollable="virtual" [rowHeight]="44" [resizable]="true">
            <ng-container *ngIf="tableResult.length > 0">
                <kendo-grid-column [sticky]="true" [minResizableWidth]="200" [maxResizableWidth]="800"  [width]="300" 
                *ngFor="let col of tableFrozenColumns;" [field]="col.field">
                    <ng-template kendoGridHeaderTemplate>
                        <div class="header-icon-wrapper wd-100 header-left-padding" >
                          <span class="TextTruncate S-M">
                           {{col.header}}
                          </span>
                        </div>
                      </ng-template>
                    <ng-template kendoGridCellTemplate let-rowData>
                        <div class="content header-left-padding" >
                            <span *ngIf="col.header =='KPI'" title={{rowData[col.field]}}
                            [ngClass]="[(rowData.IsHeader||rowData.IsBoldKPI) ? 'showToolTip TextTruncate bold-text' :'showToolTip TextTruncate',rowData.IsHeader ? 'headerKpi bold-text' : rowData['IsBoldKPI'] ? 'bold-text': '',((rowData.ParentId !==0||rowData.ParentId ==0)&&!rowData.IsHeader)?'pl-3':'']">
                            <span *ngIf="rowData.ParentId !== 0">- </span>{{rowData[col.field]}}
                            <span *ngIf="rowData['KPI Info'] =='#'">{{'('+rowData['KPI Info'] +')'}}</span>
                        </span>
                        </div>
                    </ng-template>
                </kendo-grid-column>
            </ng-container>
            <ng-container *ngIf="tableResult.length > 0">
                <kendo-grid-column [minResizableWidth]="200" *ngFor="let col of tableColumns; index as i" [maxResizableWidth]="800"
                [width]="200" title="{{col.header}}">
                <ng-template kendoGridHeaderTemplate>
                    <div class="header-icon-wrapper wd-100">
                      <span class="TextTruncate S-M">{{col.header}}</span>
                    </div>
                  </ng-template>
                <ng-template kendoGridCellTemplate let-rowData> 
                    <div tabindex="0" class="prtcmny-det-o cell-padding" 
                    [attr.title]="col.header !=='KPI' && ErrorNotation ? 'Click to view this cell logs' : ''"                                           
                    [class.table-data-right]="col.field !='KPI'" (click)="onAuditLog(rowData,col) " (dblclick)="onEditInit(rowData,col)"
                    [ngClass]="[(isValueConverted && !rowData.IsHeader && rowData['KPI Info'] === '$') ? 'kpi-set-calc-bgcolor' : '']" id="company-kpi-value">
                    <div class="content">
                        <div *ngIf="col.header !='KPI'" [ngClass]="rowData.IsBoldKPI ? 'bold-text': ''"
                            class="showToolTip TextTruncate">
                            <input autofocus
                            *ngIf="rowData[col.field + ' editable']  && rowData['KPI Info'] !='Text' && !rowData.IsHeader"
                            class="InputText companyText " pattern="/^-?\d+\.?\d*$/ "
                            (keypress)="validateMaxLength($event)" type="number" 
                            [(ngModel)]="rowData[col.field] " required
                            (blur)="onColumnEditComplete(i, col, rowData,$event) " (keyup)="validateNumber($event,rowData['KPI Info']);$event.preventDefault()"
                            (keyup.enter)="onColumnEdit($event) ">
                        <input autofocus
                            *ngIf="rowData[col.field + ' editable'] && rowData['KPI Info'] =='Text' && !rowData.IsHeader"
                            class="InputText companyText" type="text"
                            [value]="rowData[col.field]" [(ngModel)]="rowData[col.field]" required
                            (blur)="onColumnEditComplete(i, col, rowData)" (keyup.enter)="onColumnEdit($event)">
                            <div [title]="rowData['KPI Info']=='Text'? rowData[col.field]:''"
                                *ngIf="rowData[col.field]!= undefined && rowData[col.field]!=null && rowData[col.field]!=''&& !rowData.IsHeader;else empty_Text">
                                <container-element [ngSwitch]="rowData['KPI Info']">
                                    <container *ngSwitchCase="'#'">
                                        <span [title]="rowData[col.field]" *ngIf="!rowData[col.field + ' editable']"
                                            [innerHtml]="isNumberCheck(rowData[col.field]) ? (rowData[col.field] | number:'1.0-0' | minusSignToBrackets) : rowData[col.field]"></span>
                                    </container>
                                    <container *ngSwitchCase="'Text'">
                                        <span class="float-left left-align TextTruncate drop-above"  [title]="rowData[col.field]" *ngIf="!rowData[col.field + ' editable']"
                                            [innerHtml]="rowData[col.field]"></span>
                                    </container>
                                    <container *ngSwitchCase="'%'"> 
                                        <span [title]="rowData[col.field]" *ngIf="!rowData[col.field + ' editable']"
                                            [innerHtml]="isNumberCheck(rowData[col.field]) ? (rowData[col.field] | number: NumberDecimalConst.percentDecimal | minusToBracketsWithPercentage: '%'): rowData[col.field]"></span>
                                    </container>
                                    <container *ngSwitchCase="'x'">
                                        <span [title]="rowData[col.field]"
                                            *ngIf="rowData[col.field] != 'NA' && rowData[col.field] != '' && !rowData[col.field + ' editable']"
                                            [innerHtml]="isNumberCheck(rowData[col.field]) ? (rowData[col.field] | number: NumberDecimalConst.multipleDecimal)+'x': rowData[col.field]"></span>
                                    </container>
                                    <container *ngSwitchCase="'$'">
                                        <span [title]="rowData[col.field]"
                                            *ngIf="rowData[col.field] != 'NA' && rowData[col.field] != '' && !rowData[col.field + ' editable']"
                                            [innerHtml]="isNumberCheck(rowData[col.field]) ? (rowData[col.field] | number: NumberDecimalConst.currencyDecimal | minusSignToBrackets: '') : rowData[col.field]"></span>
                                    </container>
                                    <container *ngSwitchDefault>
                                    </container>
                                </container-element>
                            </div>
                            <ng-template #empty_Text class="detail-sec">
                                <span [ngClass]="rowData['KPI Info']=='Text'? 'float-left':'float-right'" *ngIf="!rowData[col.field + ' editable'] && !rowData.IsHeader">NA</span>
                                <div class="cell-padding" *ngIf="rowData.IsHeader">
                                    <div></div>
                                </div>
                            </ng-template>
                        </div>
                    </div>
                </div>
                </ng-template>
            </kendo-grid-column> 
            </ng-container>
            <ng-template kendoGridNoRecordsTemplate>
                <app-empty-state class="finacials-beta-empty-state" [imageHeight]="'41vh'" [isGraphImage]="false"
                *ngIf="tableResult.length == 0"></app-empty-state>
            </ng-template>
        </kendo-grid>
    </div>

    <div *ngIf="infoUpdate">
        <confirm-modal IsInfoPopup="true" customwidth="400px" modalTitle="Change Values in Selection" primaryButtonName="OK" (primaryButtonEvent)="CloseInfo()">
            <div>
                <div class="oprtnkpi-lh">
                    To edit cell data please select numbers in <b><i>'Absolute'</i></b> under <b><i>Values in</i></b> dropdown
                </div>
            </div>
        </confirm-modal>
    </div>

    <div *ngIf="isUploadPopupVisible" class="nep-modal nep-modal-show kpi-add-edit-modal nep-kpi-bg">
        <div class="nep-modal-mask"></div>
        <div class="nep-card nep-card-shadow nep-modal-panel nep-modal-default nep-all-upload-file">
            <app-kpi-cell-edit id="company-kpi-cell-edit" [kpiType]="companyKpiConfigData.kpiType" [dataRow]="dataRow" [tableColumns]="dataColumns" [moduleCompanyModel]="uniqueModuleCompany" (cancelButtonEvent)="cancelButtonEvent()" (confirmButtonEvent)="onSubmitButtonEvent($event)"></app-kpi-cell-edit>
          </div>
    </div>
    <div>
        <app-loader-component *ngIf="isLoader"></app-loader-component>
    </div>
</div>
</div>
 <app-foot-note [moduleId]="kpiModuleId" [companyId]="model?.portfolioCompanyID" *ngIf="tableResult?.length > 0" class="comm-footnote custom-quill-editor"></app-foot-note>
<mat-menu #menu="matMenu" [hasBackdrop]="true" class="fixed-menu">
    <app-kpitablefilters [tabname]="'Company'" [currencyCode]="model.reportingCurrencyDetail.currencyCode" [currencyID]="model.reportingCurrencyDetail.currencyID" (Kpifilter)="kpiTable_GlobalFilter($event)" [isDefaultMillion]="true" [typeField]="defaultType"></app-kpitablefilters>
</mat-menu>