import { Component, ElementRef, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { InvestCompanyService } from '../../investmentcompany/investmentcompany.service';
import { AbstractControl, AsyncValidatorFn, FormControl, FormGroup, ValidationErrors, Validators } from '@angular/forms';
import { DropDownFilterSettings } from '@progress/kendo-angular-dropdowns';
import { plusIcon, searchIcon, SVGIcon, xIcon } from '@progress/kendo-svg-icons';
import { ActivatedRoute, Router } from '@angular/router';
import { TemplateBuilderComponentService } from '../../services/template-builder-component.service';
import { catchError, map, Observable, of } from 'rxjs';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-template-builder',
  templateUrl: './template-builder-component.component.html',
  styleUrls: ['./template-builder-component.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class TemplateBuilderComponent implements OnInit  {
  @ViewChild('manageBtn', { static: false }) manageBtn!: ElementRef;  
  EMPTY_GUID="********-0000-0000-0000-************";
  panelTop = 0;
  panelLeft = 0;
  openPanel:boolean=false;
  public filterSettings: DropDownFilterSettings = {
          caseSensitive: false,
          operator: 'contains',
        };
  public virtual: any = {
      itemHeight: 40,
      pageSize: 20
    };
FilterForm: FormGroup;
reportTypes = [
  {id:1, text: 'Investment Company' },
  {id:2, text: 'CLO Page' },
  {id:3, text: 'Managed Accounts' }
];

pageSizes = ['A3', 'A4', 'A5'];
  selectedSize = 'A4';

plusIcon: SVGIcon = plusIcon;
  selectedReportType: string = 'Investment Company';
templateName:string='';
searchTerm = '';
filteredItemTypes = [];
// Item types for drag and drop
itemTypes = [];
pageOptions = Array.from({ length: 10 }, (_, i) => (i + 1));


  // Multi-page support variables
  pageCount = 1;
  currentPage = 0;
  pages: any[] = [{}];
  droppedItemsByPage: any[][] = [[]];
  titles: string[] = [''];
  subTitles: string[] = [''];

  // Canvas and header/footer
  canvasWidth = 800;
  canvasHeight = 600;
  headerCanvasWidth = 800;
  headerCanvasHeight = 100;
  footerCanvasWidth = 800;
  footerCanvasHeight = 100;
  title = "";
  subTitle = "";
  footerText = '';

  // Legacy single-page variables (for backward compatibility)
  droppedItems: any[] = [];
  showCanvas = true;
public cloCompanyList: any[] = [];
selectedCompany: any={};
// Image positioning and sizing properties
headerImagePosition = { x: 0, y: 0 };
headerImageSize = { width: 100, height: 50 };
footerImagePosition = { x: 0, y: 0 };
footerImageSize = { width: 100, height: 50 };
  draggingField: any;
  dragStartX: number;
  dragStartY: number;
  resizeStartX: number;
  resizeStartY: number;
  isEdited:boolean=false;
  templateUniqueId:string=this.EMPTY_GUID;
  public svgSearch: SVGIcon = searchIcon;
  public svgX: SVGIcon = xIcon;
  searchable:boolean=false;

  constructor(
    private readonly investCompanyService: InvestCompanyService,
    private router: Router,
    private readonly _templateBuilderComponentService: TemplateBuilderComponentService,
    private _avRoute: ActivatedRoute,
    private readonly toastrService: ToastrService,
  ) {
    if (this._avRoute.snapshot.params["templateUniqueId"]) {
      this.templateUniqueId = this._avRoute.snapshot.params["templateUniqueId"];
    }
    this.fetchInvestmentCompanyList();
  }
  ngOnInit(): void {
      this.FilterForm = new FormGroup({
      selectedCompany: new FormControl(null,Validators.required),
      selectedReportType: new FormControl(null,Validators.required),      
      templateName: new FormControl(
            '',
            [Validators.required, Validators.maxLength(40)],
            [this.templateNameAsyncValidator()]
          )
      });
      this.FilterForm.valueChanges.subscribe(() => {
        this.isEdited = true;
      });
  }

onCancel() {
      this.router.navigate(["/clo-automated-newsletter"]);
  }

  onCompanyChange(event: any) {
    this.selectedCompany = event;
  }
  fetchInvestmentCompanyList() {
    const filter = {};
    this.investCompanyService.getInvestCompanyListForClo(filter).subscribe(
      (data) => {
        this.cloCompanyList = data;

        this.selectedCompany = this.cloCompanyList[0] || null;
        if(this.templateUniqueId!=this.EMPTY_GUID){
        this.getTemplateById();
      }
      },
      (error) => {
        console.error('Error fetching investment company list', error);
      }
    );
  }



canvasItems = [];
previewMode = false;
 @ViewChild('pdfContent', { static: false }) pdfContentRef: ElementRef;
  pdfSource: string = '';
headerImageFile: File | null = null;
headerImageUrl: string | ArrayBuffer | null = null;
footerImageFile: File | null = null;
footerImageUrl: string | ArrayBuffer | null = null;
 headerItemsByPage: any[][] = [[]];
  footerItemsByPage: any[][] = [[]];
  dropZoneSize = { width: this.canvasWidth, height: 100 };
  private resizingDropZone = false;

selectedFields:any=null;

onSelectReportType(type: string) {
    this.selectedReportType = type;
  }

isDropEmpty(): boolean {
  return this.droppedItemsByPage.length === 0 ||
         this.droppedItemsByPage.every(page => page.length === 0);
}
onSubmit() {
    if (this.FilterForm.valid) {
      if(this.templateUniqueId==this.EMPTY_GUID){
        const model = {
          TemplateId: 0,
          templateUniqueId: this.templateUniqueId,
          templateName: this.FilterForm.value.templateName,
          reportTypeId: this.FilterForm.value.selectedReportType?.id,
          companyId: this.FilterForm.value.selectedCompany?.id,
        }
        this._templateBuilderComponentService.saveNewsletterTemplate(model).subscribe(response=>{
          if(response && response.status){
            this.toastrService.success(response.message, "", { positionClass: "toast-center-center" });
          }
          else{
            this.toastrService.error(response.message, "", { positionClass: "toast-center-center" });
          }
        })
      }
      this.getConfiguration(this.FilterForm.value.selectedReportType?.id || 1);
    } else {
      console.log('Form is invalid');
    }
  }

  getConfiguration(companyId: number=1) {
        this._templateBuilderComponentService.getTableList(companyId, 0, 0).subscribe({
          next: (data: any) => {
            if (!data) return;

            if(data){
              // this.editSuperscript = {}; // Initialize before mapping

        this.itemTypes = data?.map(table => {
          // Initialize editSuperscript for each field

          table.fields.forEach(field => {
            // this.editSuperscript[field.aliasName] = false;
            // // Add default formatting properties to each field
            field.format = field.format || 'currency';
            field.fontColor = field.fontColor || '#000000';
            field.bgColor = field.bgColor || '#ffffff';
            field.fontWeight = field.fontWeight || 'bold';
            // Ensure required fields have default values
            field.parent = field.parent || '';
            field.fieldAlignments = field.fieldAlignments || 'left';
            field.position = field.position || { x: 0, y: 0 };
            field.size = field.size || { width: 50, height: 20 };
          });

          return {
            ...table,
            type: "table",
            position : table.position || { x: 0, y: 0 },
            size : table.size || { width: 50, height: 20 }
          };
        });

        this.itemTypes.push({
          type: 'Contact',
          aliasName: 'Contact US',
          title:{
            value: 'Contact US',
            superscript: "0"
          }
        },
        {
          type: 'Header',
          aliasName: 'Header',
          value: "",
          fontColor :  '#000000',
          fontSize:10
        },
        {
          type: 'Footer',
          aliasName: 'Footer',
          title:{
            value: 'Footer',
            superscript: "0"
          },
          selectedFields: [],
          fieldFormats: null,
          tableData: []
        }
      );
              console.log('list', this.itemTypes);
          // If you use a separate filtered array:
          this.filteredItems(); // <-- Call the getter here
            }
            else{
              console.error('list is undefined or null');
            }
            // this.isLoader = false;
          },
          error: () => {
            // this.isLoader = false;
          }
        });
    }

templateNameAsyncValidator(): AsyncValidatorFn {
  return (control: AbstractControl): Observable<ValidationErrors | null> => {
    const value = control.value?.trim();
    if (!value) {
      return of(null);
    }

    return this._templateBuilderComponentService.checkTemplateNameExists(value).pipe(
      map((exists: boolean) => (exists && this.templateUniqueId==this.EMPTY_GUID ? { duplicateName: true } : null)),
      catchError(() => of(null)) // Don't block form on error
    );
  };
}
getTemplateById() {
  this._templateBuilderComponentService.getTemplateById(this.templateUniqueId).subscribe(response => {
    if (response) {
      // Find the full report type object
      const reportTypeObj = this.reportTypes.find(rt => rt.id === response.reportTypeId);
      // Find the full company object
      const companyObj = this.cloCompanyList.find(c => c.id === response.companyId);
      this.FilterForm.patchValue({
        selectedReportType: reportTypeObj || null,
        selectedCompany: companyObj || null,
        templateName: response.templateName
      });
    }
  });
}

filteredItems() {
  if (!this.itemTypes || !Array.isArray(this.itemTypes)) {
    this.filteredItemTypes= [];
  }
  if(this.searchTerm==null){
    this.filteredItemTypes=this.itemTypes;
  }
  else
  this.filteredItemTypes = this.itemTypes.filter(item =>
    item.aliasName?.toLowerCase().includes(this.searchTerm.toLowerCase())
  );
}
showManagePages = false;

  toggleManagePages() {
    this.showManagePages = !this.showManagePages;
    if (this.showManagePages && this.manageBtn) {
          const rect = this.manageBtn.nativeElement.getBoundingClientRect();
          this.panelTop = rect.bottom + window.scrollY;
          this.panelLeft = rect.left - 800;
        }
  }

  addPage(){
  this.onPageCountChange(this.pageCount+1)
}
slideStart = 0;
slideWindowSize = 3;

slideLeft() {
  if (this.slideStart > 0) {
    this.slideStart--;
  }
}

slideRight() {
  if (this.slideStart + this.slideWindowSize < this.pages.length) {
    this.slideStart++;
  }
}

// Optionally, update slideStart when currentPage changes
setCurrentPage(i: number) {
  this.currentPage = i;
  if (i < this.slideStart) {
    this.slideStart = i;
  } else if (i >= this.slideStart + this.slideWindowSize) {
    this.slideStart = i - this.slideWindowSize + 1;
  }
}

onPageCountChange(pageCount:number) {
  this.pageCount=pageCount;
  this.pages = Array.from({ length: this.pageCount }, () => ({}));
  while (this.droppedItemsByPage.length < this.pageCount) {
    this.droppedItemsByPage.push([]);
    this.titles.push('');
    this.subTitles.push('');
  }
  while (this.droppedItemsByPage.length > this.pageCount) {
    this.droppedItemsByPage.pop();
    this.titles.pop();
    this.subTitles.pop();
  }
  if (this.currentPage >= this.pageCount) {
    this.currentPage = this.pageCount - 1;
  }
}

nextPage() {
  if (this.currentPage < this.pageCount - 1) {
    this.currentPage++;
  }
}
prevPage() {
  if (this.currentPage > 0) {
    this.currentPage--;
  }
}

searchCancel(){  
  this.searchTerm=null;
  this.searchable=false;
  this.filteredItems();  
}

}
