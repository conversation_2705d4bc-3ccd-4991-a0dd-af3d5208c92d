import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import { catchError, from, map, Observable, switchMap, throwError } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class TemplateBuilderComponentService {
  private readonly baseUrl: string;

  constructor(
      private readonly http: HttpClient,
      @Inject('BASE_URL') baseUrl: string
    ) {
      this.baseUrl = baseUrl;
    }

    getTemplateList(): Observable<any[]> {
        return this.http
        .get<any>(`${this.baseUrl}api/clo-newsletter/get`)
          .pipe(
            map((response: any) => response),
            catchError(this.errorHandler)
          );
      }

      saveNewsletterTemplate(templateModel: any): Observable<any> {    
        return this.http
        .post(this.baseUrl + "api/clo-newsletter/save", templateModel)
          .pipe(
            map((response: any) => response),
            catchError(this.errorHandler)
          );
      }

  
      checkTemplateNameExists(name: string): Observable<boolean> {
          return this.http.get<boolean>(`${this.baseUrl}api/clo-newsletter/check-name/${encodeURIComponent(name)}`);
        }

      getTemplateById(templateUniqueId: string): Observable<any> {
            return this.http
              .get<any>(`${this.baseUrl}api/clo-newsletter/template-details/${templateUniqueId}`)
              .pipe(
                map((response: any) => response),
                catchError(this.errorHandler)
              );
        }

      getTableList( companyId: number=1,pageId: number=0, cloId: number = 0): Observable<any> {
          return this.http
            .get<any>(`${this.baseUrl}api/v1/PageConfig/getTableDetailsByPageConfig/${companyId}/${pageId}/${cloId}`)
            .pipe(
              map((response: any) => response),
              catchError(this.errorHandler)
            );
        }


      private errorHandler(error: any) {
        return throwError(error);
      }
}
