import { TestBed } from '@angular/core/testing';
import { TemplateBuilderComponentService } from './template-builder-component.service';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';

describe('TemplateBuilderComponentService', () => {
  let service: TemplateBuilderComponentService;
  let httpMock: HttpTestingController;
  const baseUrl = 'http://localhost';

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        { provide: 'BASE_URL', useValue: baseUrl }
      ]
    });
    service = TestBed.inject(TemplateBuilderComponentService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should get template list', () => {
    const mockResponse = [{ id: 1, name: 'Template 1' }];
    service.getTemplateList().subscribe(data => {
      expect(data).toEqual(mockResponse);
    });
    const req = httpMock.expectOne(`${baseUrl}api/clo-newsletter/get`);
    expect(req.request.method).toBe('GET');
    req.flush(mockResponse);
  });

  it('should save newsletter template', () => {
    const templateModel = { name: 'Test Template' };
    const mockResponse = { status: true };
    service.saveNewsletterTemplate(templateModel).subscribe(data => {
      expect(data).toEqual(mockResponse);
    });
    const req = httpMock.expectOne(`${baseUrl}api/clo-newsletter/save`);
    expect(req.request.method).toBe('POST');
    expect(req.request.body).toEqual(templateModel);
    req.flush(mockResponse);
  });

  it('should check if template name exists', () => {
    const name = 'TestName';
    service.checkTemplateNameExists(name).subscribe(data => {
      expect(data).toBeTrue();
    });
    const req = httpMock.expectOne(`${baseUrl}api/clo-newsletter/check-name/${encodeURIComponent(name)}`);
    expect(req.request.method).toBe('GET');
    req.flush(true);
  });

  it('should get template by id', () => {
    const templateId = "123";
    const mockResponse = { id: templateId, name: 'Template 123' };
    service.getTemplateById(templateId).subscribe(data => {
      expect(data).toEqual(mockResponse);
    });
    const req = httpMock.expectOne(`${baseUrl}api/clo-newsletter/template-details/${templateId}`);
    expect(req.request.method).toBe('GET');
    req.flush(mockResponse);
  });

  it('should get table list', () => {
    const companyId = 1, pageId = 2, cloId = 3;
    const mockResponse = [{ table: 'Table1' }];
    service.getTableList(companyId, pageId, cloId).subscribe(data => {
      expect(data).toEqual(mockResponse);
    });
    const req = httpMock.expectOne(`${baseUrl}api/v1/PageConfig/getTableDetailsByPageConfig/${companyId}/${pageId}/${cloId}`);
    expect(req.request.method).toBe('GET');
    req.flush(mockResponse);
  });

    it('should handle error in errorHandler', () => {
    const error = { status: 500, message: 'Server error' };
    service.getTemplateList().subscribe({
      next: () => fail('should have failed'),
      error: err => {
        // The error message returned by HttpClient includes the full response string
        expect(err.status).toBe(500);
        expect(err.message).toContain('Server Error'); // Use toContain instead of toBe
      }
    });
    const req = httpMock.expectOne(`${baseUrl}api/clo-newsletter/get`);
    req.flush(error, { status: 500, statusText: 'Server Error' });
  });

});
