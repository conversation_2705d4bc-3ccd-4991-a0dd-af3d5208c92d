import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { ActivatedRoute } from '@angular/router';
import { of } from 'rxjs';
import { TrackRecordComponent } from './track-record.component';
import { Component, Input } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { ManagedAccountService } from '../../managed-account.service';
import { KendoModule } from 'src/app/custom-modules/kendo.module';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

// Mock child component
@Component({
  selector: 'app-managed-account-data-table',
  template: '<div>Mock Data Table</div>'
})
class MockManagedAccountDataTableComponent {
  @Input() tableTitle: string;
  @Input() data: any;
  @Input() tableName: string;
  @Input() permissions: any;
}

// Mock services
class MockToastrService {
  success() {}
  error() {}
  warning() {}
  info() {}
}

class MockManagedAccountService {
  getManagedAccount() { return of({}); }
}
describe('TrackRecordComponent', () => {
  let component: TrackRecordComponent;
  let fixture: ComponentFixture<TrackRecordComponent>;
  let mockActivatedRoute: any;

  beforeEach(async () => {
    mockActivatedRoute = {
      paramMap: of({ get: (key: string) => '1' })
    };

    await TestBed.configureTestingModule({
      declarations: [
        TrackRecordComponent,
        MockManagedAccountDataTableComponent
      ],
      imports: [
        RouterTestingModule,
        KendoModule,
        BrowserAnimationsModule
      ],
      providers: [
        { provide: ActivatedRoute, useValue: mockActivatedRoute },
        { provide: ToastrService, useClass: MockToastrService },
        { provide: ManagedAccountService, useClass: MockManagedAccountService }
      ]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(TrackRecordComponent);
    component = fixture.componentInstance;
    
    // Provide permissions for both tabs using tab names
    component.permissions = {
      'PerformanceAttribution': { canView: true },
      'TrackRecord': { canView: true }
    };
    
    // Provide subPagefieldList input that will match the filtering logic
    component.subPagefieldList = [
      { name: 'PerformanceAttribution', displayName: 'Performance Attribution' },
      { name: 'TrackRecord', displayName: 'Track Record' }
    ];
    
    // Manually trigger ngOnChanges first to build navigation tabs
    component.ngOnChanges({
      subPagefieldList: {
        currentValue: component.subPagefieldList,
        previousValue: undefined,
        firstChange: true,
        isFirstChange: () => true
      }
    });
    
    // Call ngOnInit before detectChanges to ensure proper initialization
    component.ngOnInit();
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  
  it('should handle empty subPagefieldList', () => {
    component.subPagefieldList = [];
    component.ngOnChanges({
      subPagefieldList: {
        currentValue: [],
        previousValue: undefined,
        firstChange: true,
        isFirstChange: () => true
      }
    });
    component.ngOnInit();

    expect(component.navigationTabLinks.length).toBe(0);
    expect(component.selectedTab).toBeNull();
  });

  it('should handle null subPagefieldList', () => {
    component.subPagefieldList = null;
    component.ngOnChanges({
      subPagefieldList: {
        currentValue: null,
        previousValue: undefined,
        firstChange: true,
        isFirstChange: () => true
      }
    });
    component.ngOnInit();

    expect(component.navigationTabLinks.length).toBe(0);
    expect(component.selectedTab).toBeNull();
  });

  it('should not build navigation tabs when subPagefieldList change is not provided', () => {
    const originalLength = component.navigationTabLinks.length;
    
    component.ngOnChanges({
      permissions: {
        currentValue: {},
        previousValue: undefined,
        firstChange: true,
        isFirstChange: () => true
      }
    });

    expect(component.navigationTabLinks.length).toBe(originalLength);
  });

  it('should handle tab selection with null tab', () => {
    expect(() => component.selectedTabData(null)).toThrow();
  });

  it('should handle tab selection with undefined tab', () => {
    expect(() => component.selectedTabData(undefined)).toThrow();
  });

  it('should handle subPagefieldList with items that do not match filter criteria', () => {
    component.subPagefieldList = [
      { name: 'SomeOtherTab', displayName: 'Some Other Tab' },
      { name: 'AnotherTab', displayName: 'Another Tab' }
    ];
    
    component.ngOnChanges({
      subPagefieldList: {
        currentValue: component.subPagefieldList,
        previousValue: undefined,
        firstChange: true,
        isFirstChange: () => true
      }
    });

    expect(component.navigationTabLinks.length).toBe(0);
  });
});
      