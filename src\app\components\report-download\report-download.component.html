<div class="row tab-shadow pt-0" id="report-download-container">
    <div class="col-12 col-sm-12 col-lg-12 col-xl-12 col-md-12 col-xs-12 pr-0 pl-0">
        <nep-tab id="neptab" class="custom-pipeline-tab" [tabList]=reportTypes (OnSelectTab)="onTabClick($event)">
        </nep-tab>
    </div>
</div>
<container-element [ngSwitch]="selectedTab?.id">
    <app-internal-report-download *ngSwitchCase="reportTypeConstants?.InternalReportId" id="internal-report-download"></app-internal-report-download>
    <app-consolidated-report-download *ngSwitchCase="reportTypeConstants?.ConsolidatedReportId" id="consolidated-report-download"></app-consolidated-report-download>
    <app-monthly-report-download *ngSwitchCase="reportTypeConstants?.MonthlyReportId" id="monthly-report-download"></app-monthly-report-download>
    <app-growth-report-download *ngSwitchCase="reportTypeConstants?.GrowthReportId" id="growth-report-download"></app-growth-report-download>
    <div *ngSwitchCase="reportTypeConstants?.AutomatedNewsletterReportId" id="internal-report-download"> Automated Newsletter Report </div>
    <div *ngSwitchDefault></div>
</container-element>