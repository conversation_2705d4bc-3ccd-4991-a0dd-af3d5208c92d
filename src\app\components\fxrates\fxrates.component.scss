@import "../../../variables.scss";

.performance-section {
    .download-circle-loader {
            color: $nep-white!important;
        } 
    .outer-section {
        border: 1px solid $Neutral-Gray-10;
        border-radius: 0.5rem;
    }
}

.border-bottom {
    border-bottom: 1px solid $Neutral-Gray-10;
}
.icon-menu{
    &:hover{
        background: $Primary-40;
    }
}

.custom-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    .cursor-filter {
        cursor: pointer;
        &:hover{
            background: $Primary-40;
        }
    }
}

.filter-menu-container {
    padding: 0;
    min-width: 220px;
    background-color: white;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    
    .filter-menu-content {
        .filter-menu-header {
            padding: 8px;
            
            .search-box {
                position: relative;
                
                input {
                    width: 100%;
                    padding: 6px 16px;
                    border: 1px solid $Neutral-Gray-10;
                    border-radius: 4px;
                    outline: none;   
                    color: $Neutral-Gray-90;    
                    &:focus {
                        border-color: $Primary-78
                    }
                }
                
                .search-icon {
                    position: absolute;
                    right: 20px;
                    top: 48%;
                    transform: translateY(-50%);
                    color: $Primary-78;
                }
            }
        }
        
        .filter-menu-body {
            max-height: 250px;
            height: 250px;
            overflow-y: auto;
            padding: 8px 0;
            
            .select-all-option {
                padding: 8px 16px;
                margin-bottom: 8px;
                display: flex;
                align-items: center;
                height: 40px;
            }
            
            .currency-options {
                .currency-option {
                    padding: 8px 16px;
                    display: flex;
                    align-items: center;
                    height: 40px;
                    
                    &:hover {
                        background-color: #f5f5f5;
                    }
                }
            }
        }
        
        .filter-menu-actions {
            display: flex;
            justify-content: flex-end;
            padding: 8px;
            box-shadow: 0px 0px 4px 0px #00000014;
            
        }
    }
}