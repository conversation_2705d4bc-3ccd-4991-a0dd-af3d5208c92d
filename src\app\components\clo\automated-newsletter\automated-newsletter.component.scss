@import "../../../../variables";
@import '../../../../assets/dist/css/font.scss';
@import '../../../../variables';
$color-black: #000000;
$color-white: #FFFFFF;
$color-light-blue: #EBF3FF;
$color-active-blue: #D9EDFF;
$color-border: #F2F2F2;
$color-border-active: #93B0ED;
$padding-small: 0.5rem 1rem;
$padding-medium: 10px 1rem;
$border-radius-small: 4px;

.add-new-template-button-container {
    margin-left: auto;
    display: flex;
    align-items: center;
}
.template-card {
    border-bottom: 0.5px solid var(--Border-border-disabled-accent, #93B0ED);
    height: $compnay-cell-height;
    margin-bottom: 0.375rem; 
    display: flex; 
    align-items: center;
    width:100%;
}
.template-card-header {
    font-size: 14px;
    flex-grow: 1;
    padding: 6px 0px;
}

.add-new-template-button-container {
    background: var(--Color-Primary-Primary---78, #4061C7);
    height: 32px; 
    border-radius: 4px;
    border: none; 
    cursor: pointer; 
}

.add-new-template-button-container i {
    margin-right: 8px; 
}

.template-card:hover {
    cursor:pointer;
}
.template-list-content{
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding: 0.25rem 0px;
}

::-webkit-scrollbar {
    width: $review-page-scroll-width !important;
    color: $review-page-scroll-color !important;  
}

.automated-newsletter-container {

    .automated-newsletter-table {
        .automated-table-header {
            background: $color-light-blue;
            padding: $padding-small;
            color: $color-black;
            border-top-right-radius: $border-radius-small;
            border-top-left-radius: $border-radius-small;
            display: flex;
            align-items: center;
        }
        .automated-image-custom{
         padding-right: 8px;
        }
        .automated-table-body {
            .automated-table-content {
                max-height: calc(100vh - 225px);
                border-bottom-left-radius: $border-radius-small;
                border-bottom-right-radius: $border-radius-small;
                overflow-y: scroll;
            }
        }
            
    }

    .automated-delete-icon{
        height: $icon-size;
        width: $icon-size;
        border-radius: 0.25rem;   
        display: flex;
        justify-content: center;
        align-items: center;   
        cursor: pointer;           
    }

    .automated-delete-icon:hover{
        background-color: $delete-on-hover-color;
    }

    .automated-delete-icon:active{
        background-color: $delete-on-click-color;
    }
}

// Vertically center the no-content section
.newsletter-table-body {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 80vh; 
}