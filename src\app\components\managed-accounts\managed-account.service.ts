import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, throwError, Subject } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { environment } from 'src/environments/environment';
import { ManagedAccount } from './managed-account.model';
import { Commentary, DownloadTemplate} from './models/commentary.model';
import { TableFootnoteModel } from './models/table-footnote.model';
import { GetManagedCapTableValuesQuery } from './models/managed-cap-table.model';

@Injectable({
  providedIn: 'root'
})
export class ManagedAccountService {
  
 
  private myAppUrl: string;
  private goToStepSubject = new Subject<number>();
  goToStep$ = this.goToStepSubject.asObservable();

  constructor(private http: HttpClient) {
    this.myAppUrl = environment.apiBaseUrl;
  }

  emitGoToStep(step: number): void {
    this.goToStepSubject.next(step);
  }

  saveManagedAccount(managedAccount: ManagedAccount): Observable<any> {
    return this.http
      .post<any>(`${this.myAppUrl}api/managed-accounts/add`, managedAccount)
      .pipe(
        map((response: any) => response),
        catchError(this.errorHandler)
      );
  }

  updateManagedAccount(id: string, managedAccount: ManagedAccount): Observable<any> {
    return this.http
      .put<any>(`${this.myAppUrl}api/managed-accounts/${id}`, managedAccount)
      .pipe(
        map((response: any) => response),
        catchError(this.errorHandler)
      );
  }

  getManagedAccountById(managedAccountId: string): Observable<ManagedAccount> {
    return this.http
      .get<any>(`${this.myAppUrl}api/managed-accounts/${managedAccountId}`)
      .pipe(
        map((response: ManagedAccount) => response),
        catchError(this.errorHandler)
      );
  }

  getAllManagedAccounts(): Observable<any> {
    return this.http
      .get<any>(`${this.myAppUrl}api/managed-accounts`)
      .pipe(
        map((response: any) => response),
        catchError(this.errorHandler)
      );
  }

  deleteManagedAccount(accountId: number): Observable<any> {
    return this.http
      .delete<any>(`${this.myAppUrl}api/managed-accounts/delete/${accountId}`)
      .pipe(
        catchError(this.errorHandler)
      );
  }

  getTabList(pageId: number, managedAccountId: string): Observable<any> {
    return this.http
      .get<any>(`${this.myAppUrl}api/managed-accounts/get/${pageId}/${managedAccountId}`)
      .pipe(
        map((response: any) => response),
        catchError(this.errorHandler)
      );
  }

  getCommentariesByManagedAccount(managedAccountId: string): Observable<any> {
    return this.http
      .get<any>(`${this.myAppUrl}api/managed-accounts/commentaries/${managedAccountId}`)
      .pipe(
        map((response: any) => response),
        catchError(this.errorHandler)
      );
  }

  saveCommentaries(commentary: Commentary): Observable<any> {
    return this.http
      .post<any>(`${this.myAppUrl}api/managed-accounts/commentaries`, commentary)
      .pipe(
        map((response: any) => response),
        catchError(this.errorHandler)
      );
  }
  getFootnote(footnoteModel: TableFootnoteModel): Observable<any> {
    return this.http
      .post<any>(`${this.myAppUrl}api/managed-accounts/get-footnote`, footnoteModel)
      .pipe(
        map((response: any) => response),
        catchError(this.errorHandler)
      );
  }
  saveFootnote(footnoteModel: TableFootnoteModel): Observable<any> {
    return this.http
      .post<any>(`${this.myAppUrl}api/managed-accounts/footnotes/save`, footnoteModel)
      .pipe(
        map((response: any) => response),
        catchError(this.errorHandler)
      );
  }

  downloadTemplate(template: DownloadTemplate): Observable<any> {
    return this.http
      .post(`${this.myAppUrl}api/managed-accounts/import/template`, template, {
        responseType: 'blob',
        observe: 'response'
      })
      .pipe(
        catchError(this.errorHandler)
      );
  }

  uploadExcelFile(file: File, moduleName: string, managedAccountId: string): Observable<any> {
    const formData = new FormData();
    formData.append('File', file);
    formData.append('ModuleName', moduleName);
    formData.append('ManagedAccountId', managedAccountId);

    return this.http
      .post(`${this.myAppUrl}api/managed-accounts/import/excel`, formData)
      .pipe(
        catchError(this.errorHandler)
      );
  }

  getManagedCapTableValues(query: GetManagedCapTableValuesQuery): Observable<any> {
    return this.http
      .post<any>(`${this.myAppUrl}api/managedcap-table/values`, query)
      .pipe(
        map((response: any) => response),
        catchError(this.errorHandler)
      );
  }

  getManagedCapTableConfig(managedAccountId: string, moduleName: string): Observable<any> {
    return this.http
      .get<any>(`${this.myAppUrl}api/managedcap-table/config/${managedAccountId}/${moduleName}`)
      .pipe(
        map((response: any) => response),
        catchError(this.errorHandler)
      );
  }

  errorHandler(error: any) {
    return throwError(error);
  }
}
