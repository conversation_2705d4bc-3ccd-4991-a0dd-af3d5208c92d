<div class="row page-configuration">
    <div class="col-md-12 col-lg-12 col-xl-12 col-lg-12 col-12 col-xs-12">
        <div class="card" *ngFor="let config of configurationList">
            <img class="card-img-top crd-img-w" src="assets/dist/images/{{config.imagePath}}" alt="image">
            <div class="card-body mb-0">
                <h4 class="card-title">{{config.aliasName}}</h4>
                <p class="card-text">{{config.description}}</p>
            </div>
            <div id="report-template" class="text-center config-footer"> <button id="report-templates" [disabled]="!config.isActive" class="btn btn-default" (click)="openConfiguration(config)">View</button></div>
        </div>
    </div>
</div>