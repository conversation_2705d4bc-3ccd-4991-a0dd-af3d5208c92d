 <div class="row ml-4 mr-4 automated-newsletter-container">
      <div class="col-12 col-lg-12 col-xl-12 col-sm-12 col-lg-12 col-md-12 pr-0 pl-0">
         <div class="automated-newsletter-table row mr-0 ml-0">
            <div class=" row automated-table-header col-12 col-lg-12 col-xl-12 col-sm-12 col-lg-12 col-md-12 Heading2-R TextTruncate">
                    <div class="Heading2-M col-10">
                        <img src="assets/dist/images/newsletter-header.svg" alt="Newsletter Header" class="automated-image-custom" />
                        Automated Newsletter ({{ templateList?.length }})
                    </div>
                <div class="add-new-template-button-container float-right">
                    <button id="nl-create-template" 
                    kendoButton class="btn-save-clo btn btn-primary" 
                    (click)="redirectToTemplate(null)" 
                    themeColor="primary">
                        Create Newsletter
                    </button>
            </div>
            </div> 
            <div *ngIf="templateList?.length > 0; else noNewsletter" class="automated-table-body col-12 col-lg-12 col-xl-12 col-sm-12 col-lg-12 col-md-12 Heading2-R pr-0 pl-0">
                <div class="row mr-0 ml-0 automated-table-content">
                <ng-container *ngFor="let template of templateList; let i = index">
                <div class="template-card pl-3 pr-3">
                    <div class="template-list-content">
                    <span class="template-card-header" (click)="redirectToTemplate(template)" (keypress)="redirectToTemplate(template)" >{{ template.templateName }}</span><!--(click)="redirectToCompData(template.id)" (keypress)="redirectToCompData(template.id)"-->
                    <div id="delete-icon" class="automated-delete-icon" ><!--(click)="showDeletePopup(company.id, company.companyName)"-->
                        <img src='assets/dist/images/delete-company.svg' alt="Delete Icon" >
                    </div>
                    </div>
                </div>              
                </ng-container>
            </div>
            </div> 
            <ng-template #noNewsletter>
                <div class="newsletter-table-body col-12 col-lg-12 col-xl-12 col-sm-12 col-lg-12 col-md-12 Heading2-R pr-0 pl-0">
                    <div class="row mr-0 ml-0 clo-table-content">
                        <div class="col-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0 no-content-section pt-3">
                        <div class="text-center">
                            <img src="assets/dist/images/Illustrations.svg" alt="No Content" class="no-content-image">
                        </div>
                        <div class="text-center no-content-text pt-3 pb-2 Body-M">
                            Oops! It looks like we couldn't find any newsletters.<br/> Please Click on the Button Above to Add One.
                        </div>
                        </div>
                    </div>
                </div>  
         </ng-template>   
         </div>
        </div>
 </div>
 
 
