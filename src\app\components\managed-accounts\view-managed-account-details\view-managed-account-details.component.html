<div class="comp-data">
  <div class="company-name-detail">{{ accountData?.managedAccountName }}</div>
  <div class="company-facts-tab">
    <ng-container *ngFor="let navigationTab of navigationTabLinks;index as i">
      <ng-container *ngIf="isTabVisible(navigationTab.name)">
        <button id="{{navigationTab.name}}" [ngClass]="{ highlight: navigationTab.isSelected }" class="clo-fs-tab"
          (click)="selectedTabData(navigationTab)">
          {{navigationTab.aliasName}}
        </button>
      </ng-container>
    </ng-container>
  </div>

   <!-- Investment Page Tab -->
   <div *ngIf="selectedTab === ManagedAccountConstant.TAB_NAMES.Investment_Page" class="investment-page-container">
     <div class="container-fluid p-4">
       <div class="row">
         <div class="col-12">
          <app-investment-page 
            [managedAccountId]="managedAccountId"
            [permissions]="permissions"
            [subPagefieldList]="subPagefieldList">
          </app-investment-page>
         </div>
       </div>
     </div>
   </div>

   <!-- Track Record Tab -->
   <div *ngIf="selectedTab === ManagedAccountConstant.TAB_NAMES.Track_Record" class="track-record-container">
       <div class="row">
         <div class="col-12">
          <app-track-record 
            [managedAccountId]="managedAccountId"
            [permissions]="permissions"
            [subPagefieldList]="subPagefieldList">
          </app-track-record>
         </div>
       </div>
   </div>

   <!-- Portfolio Statistics Tab -->
   <div *ngIf="selectedTab === ManagedAccountConstant.TAB_NAMES.Portfolio_Statistics" class="portfolio-statistics-container">
       <div class="row">
         <div class="col-12">
          <app-portfolio-statistics 
            [managedAccountId]="managedAccountId"
            [permissions]="permissions" 
            [subPagefieldList]="subPagefieldList" 
            [accountId]="managedAccountId">
          </app-portfolio-statistics>
         </div>
       </div>
   </div>

   <!-- Investment Portfolio Tab -->
   <div *ngIf="selectedTab === ManagedAccountConstant.TAB_NAMES.Investment_Portfolio" class="investment-portfolio-container">
       <div class="row">
         <div class="col-12">
          <app-investment-portfolio 
            [managedAccountId]="managedAccountId"
            [permissions]="permissions"
            [subPagefieldList]="subPagefieldList">
          </app-investment-portfolio>
         </div>
     </div>
   </div>

   <!-- Investor Cashflow Activity Tab -->
   <div *ngIf="selectedTab === ManagedAccountConstant.TAB_NAMES.Investor_Cashflow_Activity" class="investor-cashflow-container">
       <div class="row">
         <div class="col-12">
          <app-investor-cashflow-activity 
            [managedAccountId]="managedAccountId"
            [permissions]="permissions"
            [subPagefieldList]="subPagefieldList">
          </app-investor-cashflow-activity>
         </div>
     </div>
   </div>

   <!-- Commentaries Tab -->
   <div *ngIf="selectedTab === ManagedAccountConstant.TAB_NAMES.Commentaries" class="commentaries-container">
    <div class="row m-1">
         <div class="col-12">
       <app-managed-accounts-commentary-wrapper 
            [managedAccountId]="managedAccountId"
            [isLoading]="isLoading"
            (loadingChange)="isLoading = $event"
            [permissions]="permissions"
            [subPagefieldList]="subPagefieldList">
        </app-managed-accounts-commentary-wrapper>
        </div>
     </div>
   </div>
</div>

<app-loader-component *ngIf="isLoading"></app-loader-component>