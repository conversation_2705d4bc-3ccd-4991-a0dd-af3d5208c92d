import { Component, Input, OnInit, Output, EventEmitter, OnDestroy, OnChanges, SimpleChanges, ChangeDetectorRef } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { Subscription } from 'rxjs';
import { ManagedAccountService } from '../../managed-account.service';
import { Commentary as CommentaryPayload, CommentaryUI } from '../../models/commentary.model';
import { ManagedAccountConstants, ManagedAccountPermissionConstants } from 'src/app/common/constants';

@Component({
  selector: 'app-managed-accounts-commentary-wrapper',
  templateUrl: './commentary-wrapper.component.html',
  styleUrls: ['./commentary-wrapper.component.scss']
})
export class ManagedAccountsCommentaryWrapperComponent implements OnInit, OnDestroy, OnChanges {
  @Input() managedAccountId: string;
  @Input() isLoading: boolean = false;
  @Output() loadingChange = new EventEmitter<boolean>();
  @Input() permissions: { [subFeature: string]: { canView: boolean; canEdit: boolean } };
  @Input() subPagefieldList: any[];
  readonly COMMENTARY_NAMES = ManagedAccountConstants.COMMENTARY_NAMES;

  commentaryList: CommentaryUI[] = [
    {
      tableId: 24,
      id: 0,
      sequenceNo: 1,
      name: '',
      newComment: '',
      commentaryType: ManagedAccountConstants.COMMENTARY_TYPES.Market_Commentary,
      isExpanded: false,
      isEdit: false
    },
    {
      tableId: 25,
      id: 0,
      sequenceNo: 2,
      name: '',
      newComment: '',
      commentaryType: ManagedAccountConstants.COMMENTARY_TYPES.Managed_Account_Commentary,
      isExpanded: false,
      isEdit: false
    },
  ];

  private subscription = new Subscription();
  canViewGLICommentry: boolean = false;
  canViewMarketCommentry: boolean = false;
  canEditGLICommentry: boolean = false;
  canEditMarketCommentry: boolean = false;

  constructor(
    private toastrService: ToastrService,
    private managedAccountService: ManagedAccountService,
    private cdr: ChangeDetectorRef
  ) { }

  ngOnInit(): void {
    this.loadCommentaries();
    // Set alias names on initialization if subPagefieldList is already available
    if (this.subPagefieldList) {
      this.updateCommentaryNames();
    }
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['subPagefieldList']) {
      this.updateCommentaryNames();
    }
  }


  loadCommentaries(): void {
    this.setLoading(true);

    // Use accountId directly as it should already be a string (Guid format)
    const managedAccountId = this.managedAccountId;

    if (!managedAccountId) {
      this.setLoading(false);
      this.toastrService.error('Account ID is required.', "", { positionClass: "toast-center-center" });
      return;
    }

    this.subscription.add(
      this.managedAccountService.getCommentariesByManagedAccount(managedAccountId).subscribe({
        next: (response) => {
          this.setLoading(false);
          this.processCommentaryResponse(response);
        },
        error: (error) => {
          console.error('Error loading commentaries:', error);
          this.setLoading(false);
          this.toastrService.error('Failed to load commentaries.', "", { positionClass: "toast-center-center" });
        }
      })
    );
  }

  private processCommentaryResponse(response: any[]): void {
    // Reset commentary list to default state
    this.commentaryList.forEach(item => {
      item.newComment = '';
      item.id = 0;
      item.isExpanded = false;
    });

    // Process the array response
    if (response && Array.isArray(response)) {
      response.forEach(commentary => {
        this.updateCommentaryFromResponse(commentary);
      });
    }

    // Auto-expand if content exists
    if (!this.isEmpty(this.commentaryList[0]?.newComment)) {
      this.commentaryList[0].isExpanded = true;
    }
    if (!this.isEmpty(this.commentaryList[1]?.newComment)) {
      this.commentaryList[1].isExpanded = true;
    }
  }

  private updateCommentaryFromResponse(commentary: any): void {
    // Find the matching commentary by type
    const targetCommentary = this.commentaryList.find(c => c.commentaryType === commentary.commentaryType);

    if (targetCommentary && commentary.commentaryText) {
      targetCommentary.newComment = commentary.commentaryText;
      targetCommentary.id = commentary.commentaryID;
    }
  }

  onCommentaryUpdated(updatedCommentary: CommentaryUI): void {
    const index = this.commentaryList.findIndex(c => c.tableId === updatedCommentary.tableId);
    if (index !== -1) {
      this.commentaryList[index] = { ...updatedCommentary };
      this.saveCommentary(updatedCommentary);
    }
  }

  onToggleExpansion(commentary: CommentaryUI): void {
    // Close all other panels
    this.commentaryList.forEach(item => {
      if (item.tableId !== commentary.tableId) {
        item.isExpanded = false;
        item.isEdit = false;
      }
    });

    const targetCommentary = this.commentaryList.find(c => c.tableId === commentary.tableId);
    if (targetCommentary) {
      targetCommentary.isExpanded = !targetCommentary.isExpanded;
    }
  }

  onEditToggled(commentary: CommentaryUI): void {
    const targetCommentary = this.commentaryList.find(c => c.tableId === commentary.tableId);
    if (targetCommentary) {
      // Just sync the state, don't toggle again since child already toggled it
      targetCommentary.isEdit = commentary.isEdit;
    }
  }

  private saveCommentary(commentary: CommentaryUI): void {
    this.setLoading(true);

    const managedAccountId = this.managedAccountId;

    const commentaryData = this.mapToCommentaryModel(commentary, managedAccountId);

    this.managedAccountService.saveCommentaries(commentaryData).subscribe(
      (data) => {
        this.setLoading(false);
        if (data.commentaryId) {
          this.toastrService.success(data.message, "", { positionClass: "toast-center-center" });
          commentary.isEdit = false;

          // Update the commentary ID if it was a new record
          commentary.id = data.commentaryId;

          // Refresh the commentaries to get the latest data
          this.loadCommentaries();
        }
      },
      (error) => {
        this.setLoading(false);

        let errorMessage = 'Failed to save commentary.';

        // Check if it's a specific HTML content related error
        if (error?.error?.message) {
          errorMessage = error.error.message;
        } else if (error?.message) {
          errorMessage = error.message;
        }

        console.error('Error saving commentary:', error);
        this.toastrService.error(errorMessage, "", { positionClass: "toast-center-center" });
      }
    );
  }

  private mapToCommentaryModel(commentary: CommentaryUI, managedAccountId: string): CommentaryPayload {
    // Ensure the HTML string is properly handled - similar to how view-company-details handles it
    let commentaryText = commentary.newComment || '';

    return {
      commentaryID: commentary.id || 0,
      commentaryText: commentaryText,
      commentaryType: commentary.commentaryType,
      managedAccountID: managedAccountId      
    };
  }
  
  private isEmpty(val: any): boolean {
    val = val?.replace(/<.*?>/g, '');
    return (val === undefined || val == null || val.length <= 0) ? true : false;
  }

  private setLoading(loading: boolean): void {
    this.isLoading = loading;
    this.loadingChange.emit(loading);
  }

  private updateCommentaryNames(): void {
    if (this.subPagefieldList && this.subPagefieldList.length > 0) {
      const commentaryTabs = [
        ManagedAccountPermissionConstants.ManagedAccountSubFeature.MarketCommentary,
        ManagedAccountPermissionConstants.ManagedAccountSubFeature.ManagedAccountCommentary
      ];
      const relevantFields = this.subPagefieldList.filter(f => 
        commentaryTabs.includes(f.name)
      );
      // Assign display names based on index
      if (relevantFields.length > 0) {
        relevantFields.forEach((field, index) => {
          if (this.commentaryList[index] && field.displayName) {
            this.commentaryList[index].name = field.displayName;
          }
        });
        this.cdr.detectChanges();
      }
    }
  }
}
