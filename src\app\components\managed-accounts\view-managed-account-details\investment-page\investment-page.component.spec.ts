import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { ActivatedRoute, Router } from '@angular/router';
import { of, throwError } from 'rxjs';
import { InvestmentPageComponent } from './investment-page.component';
import { Component, Input } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { ManagedAccountService } from '../../managed-account.service';
import { ManagedAccountPermissionConstants, TOASTER_MSG } from 'src/app/common/constants';
import { SimpleChanges } from '@angular/core';

// Mock child component
@Component({
  selector: 'app-managed-account-data-table',
  template: '<div>Mock Data Table</div>'
})
class MockManagedAccountDataTableComponent {
  @Input() tableTitle: string;
  @Input() data: any;
  @Input() tableName: string;
}

// Mock services
class MockToastrService {
  success = jasmine.createSpy('success');
  error = jasmine.createSpy('error');
  warning = jasmine.createSpy('warning');
  info = jasmine.createSpy('info');
}

class MockManagedAccountService {
  getManagedAccountById = jasmine.createSpy('getManagedAccountById').and.returnValue(of({}));
}

describe('InvestmentPageComponent', () => {
  let component: InvestmentPageComponent;
  let fixture: ComponentFixture<InvestmentPageComponent>;
  let mockActivatedRoute: any;
  let mockToastrService: MockToastrService;
  let mockManagedAccountService: MockManagedAccountService;
  let mockRouter: jasmine.SpyObj<Router>;

  beforeEach(async () => {
    mockActivatedRoute = {
      paramMap: of({ get: (key: string) => '123' })
    };

    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);

    await TestBed.configureTestingModule({
      declarations: [
        InvestmentPageComponent,
        MockManagedAccountDataTableComponent
      ],
      imports: [RouterTestingModule],
      providers: [
        { provide: ActivatedRoute, useValue: mockActivatedRoute },
        { provide: ToastrService, useClass: MockToastrService },
        { provide: ManagedAccountService, useClass: MockManagedAccountService },
        { provide: Router, useValue: routerSpy }
      ]
    }).compileComponents();

    mockToastrService = TestBed.inject(ToastrService) as jasmine.SpyObj<ToastrService>;
    mockManagedAccountService = TestBed.inject(ManagedAccountService) as jasmine.SpyObj<ManagedAccountService>;
    mockRouter = TestBed.inject(Router) as jasmine.SpyObj<Router>;
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(InvestmentPageComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('ngOnInit', () => {
    it('should initialize component and load investment page', () => {
      spyOn(component, 'loadInvestmentPage');
      
      // Set the managedAccountId input property before calling ngOnInit
      component.managedAccountId = '123';
      
      component.ngOnInit();
      
      expect(component.isLoading).toBe(false);
      expect(component.managedAccountId).toBe('123');
      expect(component.loadInvestmentPage).toHaveBeenCalledWith('123');
    });

    it('should not call loadInvestmentPage when managedAccountId is not provided', () => {
      spyOn(component, 'loadInvestmentPage');
      
      // Don't set managedAccountId - it should be undefined
      component.ngOnInit();
      
      expect(component.isLoading).toBe(false);
      expect(component.managedAccountId).toBeUndefined();
      expect(component.loadInvestmentPage).not.toHaveBeenCalled();
    });
  });

  describe('ngOnChanges', () => {
    it('should build navigation tab links when subPagefieldList changes', () => {
      component.subPagefieldList = [
        { name: ManagedAccountPermissionConstants.ManagedAccountSubFeature.Performance, displayName: 'Performance', aliasName: 'Perf' }
      ];
      
      const changes: SimpleChanges = {
        subPagefieldList: {
          currentValue: [{ name: 'test', displayName: 'Test' }],
          previousValue: null,
          firstChange: true,
          isFirstChange: () => true
        }
      };

      component.ngOnChanges(changes);

      expect(component.navigationTabLinks.length).toBeGreaterThan(0);
    });

    it('should not build navigation tab links when other properties change', () => {
      const initialLinksLength = component.navigationTabLinks.length;
      const changes: SimpleChanges = {
        permissions: {
          currentValue: {},
          previousValue: null,
          firstChange: true,
          isFirstChange: () => true
        }
      };

      component.ngOnChanges(changes);

      expect(component.navigationTabLinks.length).toBe(initialLinksLength);
    });
  });

  describe('buildNavigationTabLinks behavior', () => {
    beforeEach(() => {
      component.subPagefieldList = [
        { name: ManagedAccountPermissionConstants.ManagedAccountSubFeature.ManagedAccountFacts, displayName: 'Account Facts', aliasName: 'Facts' },
        { name: ManagedAccountPermissionConstants.ManagedAccountSubFeature.InvestmentSummary, displayName: 'Investment Summary', aliasName: 'Summary' },
        { name: ManagedAccountPermissionConstants.ManagedAccountSubFeature.Performance, displayName: 'Performance', aliasName: 'Performance', isSelected: true },
        { name: ManagedAccountPermissionConstants.ManagedAccountSubFeature.NAVData, displayName: 'NAV Data', aliasName: 'NAV' },
        { name: ManagedAccountPermissionConstants.ManagedAccountSubFeature.IncomeDistributionsInvestmentPage, displayName: 'Income Distributions', aliasName: 'Income' },
        { name: 'Other', displayName: 'Other Tab', aliasName: 'Other' }
      ];
    });

    it('should set managedAccountFactsTitle and investmentSummaryTitle', () => {
      const changes: SimpleChanges = {
        subPagefieldList: { currentValue: component.subPagefieldList, previousValue: null, firstChange: true, isFirstChange: () => true }
      };
      
      component.ngOnChanges(changes);

      expect(component.managedAccountFactsTitle).toBe('Account Facts');
      expect(component.investmentSummaryTitle).toBe('Investment Summary');
    });

    it('should use aliasName when displayName is not available', () => {
      component.subPagefieldList[0].displayName = null;
      component.subPagefieldList[1].displayName = undefined;

      const changes: SimpleChanges = {
        subPagefieldList: { currentValue: component.subPagefieldList, previousValue: null, firstChange: true, isFirstChange: () => true }
      };
      
      component.ngOnChanges(changes);

      expect(component.managedAccountFactsTitle).toBe('Facts');
      expect(component.investmentSummaryTitle).toBe('Summary');
    });

    it('should build navigation tab links for investment page tabs only', () => {
      const changes: SimpleChanges = {
        subPagefieldList: { currentValue: component.subPagefieldList, previousValue: null, firstChange: true, isFirstChange: () => true }
      };
      
      component.ngOnChanges(changes);

      expect(component.navigationTabLinks.length).toBe(3);
      expect(component.navigationTabLinks.map(tab => tab.name)).toEqual([
        ManagedAccountPermissionConstants.ManagedAccountSubFeature.Performance,
        ManagedAccountPermissionConstants.ManagedAccountSubFeature.NAVData,
        ManagedAccountPermissionConstants.ManagedAccountSubFeature.IncomeDistributionsInvestmentPage
      ]);
    });

    it('should preserve isSelected property', () => {
      const changes: SimpleChanges = {
        subPagefieldList: { currentValue: component.subPagefieldList, previousValue: null, firstChange: true, isFirstChange: () => true }
      };
      
      component.ngOnChanges(changes);

      const performanceTab = component.navigationTabLinks.find(tab => tab.name === ManagedAccountPermissionConstants.ManagedAccountSubFeature.Performance);
      expect(performanceTab?.isSelected).toBe(true);
    });

    it('should handle empty subPagefieldList', () => {
      component.subPagefieldList = [];

      const changes: SimpleChanges = {
        subPagefieldList: { currentValue: [], previousValue: null, firstChange: true, isFirstChange: () => true }
      };
      
      component.ngOnChanges(changes);

      expect(component.navigationTabLinks.length).toBe(0);
      expect(component.managedAccountFactsTitle).toBe('');
      expect(component.investmentSummaryTitle).toBe('');
    });

    it('should handle null subPagefieldList', () => {
      component.subPagefieldList = null;

      const changes: SimpleChanges = {
        subPagefieldList: { currentValue: null, previousValue: null, firstChange: true, isFirstChange: () => true }
      };

      expect(() => component.ngOnChanges(changes)).not.toThrow();
    });
  });

  describe('loadInvestmentPage', () => {
    beforeEach(() => {
      component.isLoading = false;
    });

    it('should load managed account data successfully', () => {
      const mockData = { id: '123', managedAccountName: 'Test Account' } as any;
      mockManagedAccountService.getManagedAccountById.and.returnValue(of(mockData));

      component.loadInvestmentPage('123');

      expect(component.isLoading).toBe(false);
      expect(component.managedAccountData).toEqual(jasmine.objectContaining({
        id: '123',
        managedAccountName: 'Test Account'
      }));
      expect(mockManagedAccountService.getManagedAccountById).toHaveBeenCalledWith('123');
    });

    it('should handle null data response', () => {
      mockManagedAccountService.getManagedAccountById.and.returnValue(of(null));

      component.loadInvestmentPage('123');

      expect(component.isLoading).toBe(false);
      expect(mockToastrService.error).toHaveBeenCalledWith(TOASTER_MSG.NOT_FOUND, '', { positionClass: TOASTER_MSG.POS_CENTER });
      expect(mockRouter.navigate).toHaveBeenCalledWith(['/managed-accounts']);
    });

    it('should handle service error', () => {
      const error = new Error('Service error');
      mockManagedAccountService.getManagedAccountById.and.returnValue(throwError(() => error));

      component.loadInvestmentPage('123');

      expect(component.isLoading).toBe(false);
      expect(mockToastrService.error).toHaveBeenCalledWith(TOASTER_MSG.NOT_FOUND, '', { positionClass: TOASTER_MSG.POS_CENTER });
      expect(mockRouter.navigate).toHaveBeenCalledWith(['/managed-accounts']);
    });

    it('should set loading state correctly', () => {
      mockManagedAccountService.getManagedAccountById.and.returnValue(of({ id: '123' }));

      component.loadInvestmentPage('123');

      expect(mockManagedAccountService.getManagedAccountById).toHaveBeenCalled();
    });
  });

  describe('redirectToEditPage', () => {
    beforeEach(() => {
      component.managedAccountId = '123';
    });

    it('should navigate to add-managed-account with correct parameters', () => {
      const step = 2;

      component.redirectToEditPage(step);

      expect(mockRouter.navigate).toHaveBeenCalledWith(['/add-managed-account', '123'], { queryParams: { step: step } });
    });

    it('should handle different step values', () => {
      component.redirectToEditPage(1);
      expect(mockRouter.navigate).toHaveBeenCalledWith(['/add-managed-account', '123'], { queryParams: { step: 1 } });

      component.redirectToEditPage(5);
      expect(mockRouter.navigate).toHaveBeenCalledWith(['/add-managed-account', '123'], { queryParams: { step: 5 } });
    });
  });

  describe('Input Properties', () => {
    it('should handle permissions input', () => {
      const permissions = { 
        'Performance': { canView: true, canEdit: false },
        'NAVData': { canView: true, canEdit: true },
        'Income_Distributions': { canView: true, canEdit: false }
      };
      component.permissions = permissions;

      expect(component.permissions).toEqual(permissions);
    });

    it('should handle subPagefieldList input', () => {
      const subPagefieldList = [{ name: 'test', displayName: 'Test' }];
      component.subPagefieldList = subPagefieldList;

      expect(component.subPagefieldList).toEqual(subPagefieldList);
    });
  });

  describe('Component Properties', () => {
    it('should have correct initial values', () => {
      expect(component.isLoading).toBe(true);
      expect(component.navigationTabLinks).toEqual([]);
      expect(component.managedAccountFactsTitle).toBe('');
      expect(component.investmentSummaryTitle).toBe('');
      expect(component.ManagedAccountPermissionConstants).toBe(ManagedAccountPermissionConstants);
    });
  });
});
