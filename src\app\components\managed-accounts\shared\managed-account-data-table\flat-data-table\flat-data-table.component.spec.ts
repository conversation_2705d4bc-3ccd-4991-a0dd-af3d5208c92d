import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { FlatDataTableComponent } from './flat-data-table.component';
import { ManagedAccountService } from '../../../managed-account.service';
import { KendoModule } from 'src/app/custom-modules/kendo.module';
import { of } from 'rxjs';

describe('FlatDataTableComponent', () => {
  let component: FlatDataTableComponent;
  let fixture: ComponentFixture<FlatDataTableComponent>;

  const mockManagedAccountService = {
    getManagedCapTableValues: jasmine.createSpy('getManagedCapTableValues').and.returnValue(of({}))
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [FlatDataTableComponent],
      imports: [HttpClientTestingModule, KendoModule],
      providers: [
        { provide: ManagedAccountService, useValue: mockManagedAccountService }
      ]
    });
    fixture = TestBed.createComponent(FlatDataTableComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
