import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { ActivatedRoute } from '@angular/router';
import { of } from 'rxjs';
import { InvestorCashflowActivityComponent } from './investor-cashflow-activity.component';
import { Component, Input } from '@angular/core';
import { KendoModule } from 'src/app/custom-modules/kendo.module';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

// Mock child component
@Component({
  selector: 'app-managed-account-data-table',
  template: '<div>Mock Data Table</div>'
})
class MockManagedAccountDataTableComponent {
  @Input() tableTitle: string;
  @Input() data: any;
  @Input() tableName: string;
}

describe('InvestorCashflowActivityComponent', () => {
  let component: InvestorCashflowActivityComponent;
  let fixture: ComponentFixture<InvestorCashflowActivityComponent>;
  let mockActivatedRoute: any;

  beforeEach(async () => {
    mockActivatedRoute = {
      paramMap: of({ get: (key: string) => '1' })
    };

    await TestBed.configureTestingModule({
      declarations: [
        InvestorCashflowActivityComponent,
        MockManagedAccountDataTableComponent
      ],
      imports: [
        RouterTestingModule,
        KendoModule,
        BrowserAnimationsModule
      ],
      providers: [
        { provide: ActivatedRoute, useValue: mockActivatedRoute }
      ]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(InvestorCashflowActivityComponent);
    component = fixture.componentInstance;
    // Don't call detectChanges here to avoid triggering ngOnInit
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    // Check initial values before ngOnInit
    expect(component.isLoading).toBe(true);
    expect(component.selectedTab).toBe('Investor CashFlow Activity');
    expect(component.navigationTabLinks.length).toBe(0); // Initially empty until subPagefieldList is provided
    expect(component.ManagedAccountConstant).toBeDefined();
  });

  it('should set loading to false on ngOnInit', () => {
    expect(component.isLoading).toBe(true); // Should be true initially
    fixture.detectChanges(); // This triggers ngOnInit
    expect(component.isLoading).toBe(false); // Should be false after ngOnInit
  });
});
