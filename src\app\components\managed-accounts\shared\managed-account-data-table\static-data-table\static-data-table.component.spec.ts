import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';

import { StaticDataTableComponent } from './static-data-table.component';

describe('StaticDataTableComponent', () => {
  let component: StaticDataTableComponent;
  let fixture: ComponentFixture<StaticDataTableComponent>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [StaticDataTableComponent],
      imports: [HttpClientTestingModule]
    });
    fixture = TestBed.createComponent(StaticDataTableComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
