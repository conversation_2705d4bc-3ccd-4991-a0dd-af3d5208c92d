import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { TemplateBuilderComponentService } from '../services/template-builder-component.service';

@Component({
  selector: 'app-automated-newsletter',
  templateUrl: './automated-newsletter.component.html',
  styleUrls: ['./automated-newsletter.component.scss']
})
export class AutomatedNewsletterComponent {
  templateList:any[]=[];
  constructor(private router: Router,
    private _templateBuilderComponentService:TemplateBuilderComponentService,
  ){
    this.getTemplates();
  }

  redirectToTemplate(template:any) {
    if(template == null){
      localStorage.setItem("headerName","Create Newsletter Template");
      this.router.navigate(["/clo-newsletter-template"]);
    }
    else{
      localStorage.setItem("headerName",template.templateName);
      this.router.navigate(["/clo-newsletter-template", template.templateUniqueId]);
    }
  }

  getTemplates(){
    this._templateBuilderComponentService.getTemplateList().subscribe(result=>{
      if(result){
        this.templateList=result;
      }
    })
  }

}
