<div class="automated-newsletter pt-0">
  <!-- Header -->
  <div class="item-header">
    <span class="Heading2-M">Automated Newsletter</span>
    <div class="actions float-right">
      <button kendoButton imageUrl="assets/dist/images/customEyeIcon.svg" themeColor="primary" title="Preview" fillMode="clear" class="mr-2" > 
        Preview</button><!--(click)="showPreview()"-->
      <button class="kendo-custom-button Body-R cancel-btn mr-2 mt-0"  kendoButton type="button"            
            themeColor="primary"  fillMode="outline" (click)="onCancel()">Cancel</button>      
      <button class="kendo-custom-button Body-R save-btn mt-0" kendoButton type="button"
            themeColor="primary" >Save</button><!--(click)="save()"-->
    </div>
  </div>

  <!-- Controls -->
  <section class="controls w-100 pt-4 pl-4 pr-4 pb-4">
  <form [formGroup]="FilterForm" class="row col-12" style="align-content: center;
    align-items: center;">
    
    <!-- Report Type -->
    <div class="col-lg-3 col-sm-12 col-xs-12">
      <label for="reportType">Report Type<span class="required">*</span></label>
      <kendo-combobox
        id="reportType"
        [clearButton]="false"
        [kendoDropDownFilter]="filterSettings"
        formControlName="selectedReportType"
        [fillMode]="'solid'"
        [filterable]="true"
        name="reportType"
        [virtual]="virtual"
        class="w-100 k-custom-solid-dropdown k-dropdown-height-36"
        [size]="'medium'"
        [data]="reportTypes"
        [valuePrimitive]="false"
        textField="text"
        placeholder="Type here..."
        (valueChange)="onSelectReportType($event)"
        valueField="id"
        [popupSettings]="{ popupClass: 'custom-popup' }"
      ></kendo-combobox>
      <div *ngIf="FilterForm.get('selectedReportType')?.invalid && FilterForm.get('selectedReportType')?.touched" class="required">
        Report Type is required.
      </div>
    </div>

    <!-- Company Name -->
    <div class="col-lg-3 col-sm-12 col-xs-12">
      <label for="companyName">Company Name<span class="required">*</span></label>
      <kendo-combobox
        id="companyName"
        [clearButton]="false"
        [kendoDropDownFilter]="filterSettings"
        formControlName="selectedCompany"
        [fillMode]="'solid'"
        [filterable]="true"
        name="selectedCompany"
        [virtual]="virtual"
        class="w-100 k-custom-solid-dropdown k-dropdown-height-36"
        [size]="'medium'"
        [data]="cloCompanyList"
        [valuePrimitive]="false"
        textField="companyName"
        placeholder="Type here..."
        (valueChange)="onCompanyChange($event)"
        valueField="id"
        [popupSettings]="{ popupClass: 'custom-popup' }"
      ></kendo-combobox>
      <div *ngIf="FilterForm.get('selectedCompany')?.invalid && FilterForm.get('selectedCompany')?.touched" class="required">
        Company Name is required.
      </div>
    </div>

    <!-- Template Name -->
    <div class="col-lg-4 col-sm-12 col-xs-12">
      <label for="templateName">Template Name<span class="required">*</span></label>
      <kendo-textbox
        formControlName="templateName"
        id="templateName"
        class="w-100"
        placeholder="Enter template name"
      ></kendo-textbox>
      <div *ngIf="FilterForm.get('templateName')?.invalid && !FilterForm.get('templateName')?.hasError('maxlength') && !FilterForm.get('templateName')?.hasError('duplicateName') && FilterForm.get('templateName')?.touched" class="required">
        Template Name is required.
      </div>
      <div *ngIf="FilterForm.get('templateName')?.hasError('maxlength') && FilterForm.get('templateName')?.touched" class="required">
        A maximum of 40 characters is permitted.
      </div>
      <div *ngIf="FilterForm.get('templateName')?.hasError('duplicateName') && FilterForm.get('templateName')?.touched" class="required">
        This template is already available.
      </div>
    </div>

    <!-- Apply Button -->
     <div class="col-lg-2 col-sm-3 col-xs-3 mt-4 apply-btn-container">
      <button type="button"
            [disabled]="FilterForm.invalid"
            class="kendo-custom-button Body-R apply-btn"
            kendoButton
            themeColor="primary"
            fillMode="outline"
            (click)="onSubmit()">
      Apply
    </button>
     </div>
    
  </form>
</section>

  <!-- Main Content -->
  <div class="main-layout">
    <!-- Line Items Panel -->
     
    <aside class="line-items" *ngIf="filteredItemTypes.length>0">
      <div class="row col-12 m-0" style="padding:10px;border-bottom:1px solid #e6e6e6;">
        <div class="Heading2-M float-left col-10 pl-0" *ngIf="!searchable">
          Line Items
        </div>
        <div class="float-right text-center ml-1" *ngIf="!searchable">
          <button kendoButton themeColor="primary" fillMode="clear" [svgIcon]="svgSearch" title="Search"
          (click)="searchable=true;"></button>
        </div>
        <div class="col-sm-12 col-md-12 col-lg-12 search-box" *ngIf="searchable">
            <kendo-textbox size="small"
              class="request-details-search dr-search-width kendo-text-search" selectOnFocus="false"
              (valueChange)="filteredItems()"
              [(ngModel)]="searchTerm"
            placeholder="Search items...">
              <ng-template kendoTextBoxSuffixTemplate *ngIf="!searchTerm && searchable">
                <button class="kendo-text-button" themeColor="primary" fillMode="clear" kendoButton [svgIcon]="svgSearch"></button>
              </ng-template>
              <ng-template kendoTextBoxSuffixTemplate *ngIf="searchTerm">
                <button class="kendo-text-button" themeColor="primary" fillMode="clear" kendoButton [svgIcon]="svgX" (click)="searchCancel()"></button>
              </ng-template>
            </kendo-textbox>
          </div>
      </div>
      <div class="row col-12 m-0 Body-R" *ngIf="searchable && searchTerm">
        Search Results : {{ filteredItemTypes.length | number: '2.0' }}
      </div>
      <div class="line-items-container" style="padding:10px">            
          <div  class="list-container">
            <div *ngFor="let item of filteredItemTypes" draggable="true"
                    (dragstart)="onDragStart($event, item)"
                    (dragend)="onDragEnd($event, item)" class="list-item">
                    <div class="handle">
                      <img src="assets/dist/images/6dots.svg" alt="">
                      </div>
                    {{ item.aliasName }}
            </div>
          </div>
      </div>
    </aside>

    <!-- Newsletter Template Panel -->
    <section class="newsletter-template" [ngStyle]="{'width': isDropEmpty() ? '100%' : '60%'}">
      <div class="template-header">
        <div class="Heading2-M">Newsletter Template</div>
        <button kendoButton #manageBtn [disabled]="FilterForm.invalid"
         imageUrl="assets/dist/images/svgPlusOutline.svg" themeColor="primary" title="Manage Page" fillMode="clear" class="mr-2" 
         (click)="toggleManagePages()">
           Manage Page</button>
      </div>
      <div class="template-header-border"></div>
      <div *ngIf="(!isDropEmpty()|| (pages.length>0 && FilterForm.valid)); else noTemplate">

      <div class="slide-view mb-3 mt-3" style="display: flex; gap: 8px; align-items: center; justify-content: center;">
        <button class="btn btn-light" (click)="slideLeft()" [disabled]="slideStart === 0">&lt;</button>
        <div style="display: flex; gap: 8px;">
          <div *ngFor="let page of pages | slice:slideStart:(slideStart+slideWindowSize); let i = index"
              (click)="setCurrentPage(i + slideStart)"
              [ngClass]="{'selected-slide': (i + slideStart) === currentPage}"
              style="width: 60px; height: 80px; border: 2px solid #ccc; display: flex; align-items: center; justify-content: center; cursor: pointer; background: #f9f9f9; position: relative;">
            <span>Page {{ i + slideStart + 1 }}</span>
          </div>
        </div>
        <button class="btn btn-light" (click)="slideRight()" [disabled]="slideStart + slideWindowSize >= pages.length">&gt;</button>
      </div>
      <div *ngFor="let page of pages; let i = index" [ngStyle]="{ display: i === currentPage ? 'block' : 'none' }">
        <div  *ngIf="i === currentPage" 
        [style.width.px]="headerCanvasWidth"
        class="template-content">

          <div class="section">

          </div>
        </div>
      </div>
      </div>
      <ng-template #noTemplate>
        <div class="col-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0 no-content-section pt-3 drop-zone resizable-drop-zone">
          <div class="text-center">
            <img src="assets/dist/images/newsletterListDefault.svg" alt="No Content" class="no-content-image">
          </div>
        </div>
      </ng-template>
       <!-- Floating Manage Pages Panel -->
      <kendo-window class="manage-pages"
        *ngIf="showManagePages"
        title="Manage Pages"
        [width]="900"
        [height]="200"        
        [top]="panelTop"
        [left]="panelLeft"
      >
        
        <div class="row col-12">
          <div class="col-6">
          <label>Page Size</label>
          <kendo-dropdownlist
            [data]="pageSizes"
            [(ngModel)]="selectedSize"
            [defaultItem]="'Select size'"
          ></kendo-dropdownlist>
        </div>
        <div class="col-6">
         <label>Number of Pages</label>
          <kendo-numerictextbox
            format="##"
            [(ngModel)]="pageCount"
            [min]="1"
            (valueChange)="onPageCountChange($event)"
          ></kendo-numerictextbox>

        </div>

        </div>

        
      </kendo-window>
    </section>

    <!-- Panel Settings -->
    <aside #panelSettings class="panel-settings" *ngIf="openPanel">
      <div class="side-pane">
        <div class="side-header">
            <div class="title-h float-left">Panel Settings</div>
            <div class="float-right close-icon">
                <a (click)="cancelPanel()"> <img src="assets/dist/images/fix-close.svg" alt="" /></a>
            </div>
        </div><!--(click)="cancelAddDoc()"-->
        <label>Background Color:</label>
      <input type="color" />

      <label>Footnotes:</label>
      <textarea rows="4"></textarea>
        <div class="button-group fixed-bottom custom-bottom">
            <div class="float-right">
                <!-- <button type="button" class="kendo-custom-button Body-R apply-btn mr-3" fillMode="outline" kendoButton
                    (click)="cancelAddDoc()" themeColor="primary">Cancel</button>
                <button (click)="addNewDocumentType()" [disabled]="!newDocumentType?.trim()"
                    class="kendo-custom-button Body-R apply-btn" kendoButton themeColor="primary">Save</button> -->
            </div>
        </div>

    </div>
    </aside>
    
  </div>
  <!-- PDF Preview Modal -->
  </div>