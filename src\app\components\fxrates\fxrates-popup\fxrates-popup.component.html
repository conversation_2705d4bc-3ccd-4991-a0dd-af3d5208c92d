<div class="fxrates-popup-modal">
    <div class="fxrates-popup-content">
        <form [formGroup]="form" (ngSubmit)="onSubmit()" (click)="$event.stopPropagation()">
            <!-- Period -->
            <div class="popup-row">
                <div class="popup-field">
                    <label class="Caption-M">Period <span class="required">*</span></label>
                    <kendo-combobox [clearButton]="false" [fillMode]="'solid'" [data]="periodOptions"
                        [valueField]="'periodName'" [textField]="'periodName'" [valuePrimitive]="true" [filterable]="true"
                        placeholder="Select Period" formControlName="period" (valueChange)="onFormChange()"
                        [ngClass]="getPeriodComboClass()">
                    </kendo-combobox>
                    <div *ngIf="form.get('period')?.invalid && form.get('period')?.touched" class="validation-error">
                        Period is required.
                    </div>
                </div>
              
            </div>
            <div class="popup-row" *ngIf="form.get('period').value === 'Period Range'">
                <div class="popup-field">
                    <label class="Caption-M">Date Range <span class="required">*</span></label>
                    <kendo-daterange class="k-daterange-custom">
                        <kendo-dateinput
                            kendoDateRangeStartInput
                            [min]="minDate"
                            [max]="maxDate"
                            [format]="'yyyy-MM-dd'"
                            placeholder="From"
                            class="k-custom-solid-dropdown k-dropdown-width-260"
                            formControlName="fromDate"
                            (valueChange)="onFormChange()">
                        </kendo-dateinput>
                        <kendo-dateinput
                            kendoDateRangeEndInput
                            [min]="minDate"
                            [max]="maxDate"
                            [format]="'yyyy-MM-dd'"
                            placeholder="To"
                            class="k-custom-solid-dropdown k-dropdown-width-260 ml-2"
                            formControlName="toDate"
                            (valueChange)="onFormChange()">
                        </kendo-dateinput>
                    </kendo-daterange>
                    <div *ngIf="(form.get('fromDate')?.invalid || form.get('toDate')?.invalid) && (form.get('fromDate')?.touched || form.get('toDate')?.touched)"
                        class="validation-error">
                        From and To dates are required.
                    </div>
                </div>
            </div>

            <!-- Frequency and Financial Year End -->
            <div class="popup-row">
                <div class="popup-field">
                    <label class="Caption-M">Frequency <span class="required">*</span></label>
                    <kendo-multiselect [clearButton]="false" [data]="frequencyOptions" [valueField]="'frequencyId'" [autoClose]="false"
                        [textField]="'frequencyName'" [checkboxes]="true" [filterable]="true"
                        placeholder="Select Frequency" formControlName="frequency" required
                        class="k-multiselect-custom k-dropdown-width-260 k-multiselect-grey-chip"
                        [tagMapper]="tagMapper" (valueChange)="onFormChange()">
                    </kendo-multiselect>
                    <div *ngIf="form.get('frequency')?.invalid && form.get('frequency')?.touched"
                        class="validation-error">
                        Frequency is required.
                    </div>
                </div>
                <div class="popup-field">
                    <label class="Caption-M">Financial Year End <span class="required">*</span></label>
                      <kendo-multiselect [clearButton]="false" [data]="financialYearEndOptions" [valueField]="'month'" [autoClose]="false"
                        [textField]="'fyEndName'" [checkboxes]="true" [filterable]="true"
                        placeholder="Select Financial Year End" formControlName="financialYearEnd"
                        class="k-multiselect-custom k-dropdown-width-260 k-multiselect-grey-chip"
                        [tagMapper]="tagMapper" (valueChange)="onFormChange()"
                        [readOnly]="form.get('financialYearEndOptions') && !form.get('financialYearEnd')?.disabled">
                    </kendo-multiselect>
                    <div *ngIf="form.get('financialYearEnd')?.invalid && form.get('financialYearEnd')?.touched"
                        class="validation-error">
                        Financial Year End is required.
                    </div>
                </div>
            </div>

            <!-- Currency Selection and FX Methodology -->
            <div class="popup-row">
                <div class="popup-field">
                    <label class="Caption-M">
                        Currency Selection <span class="required">*</span>
                        <span><img
                            tooltipPosition="top"
                            tooltipStyleClass="bg-tooltip-fund-color"
                            class="ml-1"
                            src="assets/dist/images/info-icon.svg"
                            alt="info-icon"
                          /></span>
                    </label>
                    <kendo-multiselecttree [checkableSettings]="{ checkChildren: true, checkParents: true }"
                        class="k-dropdown-width-260 k-multiselect-custom k-multiselect-custom-tree k-multiselect-chip-100 k-multiselect-grey-chip"
                        [clearButton]="false" [fillMode]="'solid'" kendoMultiSelectTreeExpandable
                        [kendoMultiSelectTreeHierarchyBinding]="currencySelectionOptions" [textField]="'label'"
                        [valueField]="'value'" childrenField="children" #multiselecttree adaptiveMode="auto"
                        [checkboxes]="true" formControlName="currencySelection" required [tagMapper]="tagMapper"
                        placeholder="Select Currency" [filterable]="true" (valueChange)="onCurrencyValueChange($event)">
                        <ng-template kendoMultiSelectTreeHeaderTemplate>
                            <div class="inline-container">
                                <input type="checkbox" 
                                    class="k-checkbox k-checkbox-md k-rounded-md chkCopyTo"
                                    kendoCheckBox 
                                    [checked]="isAllCurrenciesSelected" 
                                    [indeterminate]="isCurrencySelectionIndeterminate"
                                    (change)="onSelectAllCurrencies($event)" />
                                <kendo-label>
                                    Select All
                                </kendo-label>
                            </div>
                        </ng-template>
                    </kendo-multiselecttree>
                    <div *ngIf="form.get('currencySelection')?.invalid && form.get('currencySelection')?.touched"
                        class="validation-error">
                        Currency Selection is required.
                    </div>
                </div>
                <div class="popup-field">
                    <label class="Caption-M">FX Methodology <span class="required">*</span></label>
                    <kendo-multiselect [clearButton]="false" [data]="fxMethodologyOptions" [autoClose]="false"
                        [valueField]="'methodologyId'" [textField]="'methodology'" [checkboxes]="true"
                        [filterable]="true" placeholder="Select FX Methodology" formControlName="fxMethodology" required
                        class="k-multiselect-custom k-dropdown-width-260 k-multiselect-grey-chip"
                        [tagMapper]="tagMapper" (valueChange)="onFormChange()">
                    </kendo-multiselect>
                    <div *ngIf="form.get('fxMethodology')?.invalid && form.get('fxMethodology')?.touched"
                        class="validation-error">
                        FX Methodology is required.
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="popup-actions">
                <button type="button" class="kendo-custom-button Body-R apply-btn mr-2" fillMode="outline" kendoButton (click)="onCancel()" themeColor="primary">Cancel</button>
                <button type="submit" class="kendo-custom-button Body-R apply-btn" kendoButton themeColor="primary" [disabled]="!isFormChanged || form.invalid">Apply</button>
            </div>
        </form>
    </div>
</div>