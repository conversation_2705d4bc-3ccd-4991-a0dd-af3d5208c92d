import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { Component, Input } from '@angular/core';
import { ManagedAccountDataTableComponent } from './managed-account-data-table.component';
import { KendoModule } from 'src/app/custom-modules/kendo.module';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { ToastrModule, ToastrService } from 'ngx-toastr';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ManagedAccountService } from '../../managed-account.service';
import { MiscellaneousService } from 'src/app/services/miscellaneous.service';
import { of, Observable } from 'rxjs';
import { throwError } from 'rxjs';

// Mock child components
@Component({
  selector: 'app-data-table-footnote',
  template: '<div></div>',
})
class MockDataTableFootnoteComponent {
  @Input() footnotes: any[];
}

@Component({
  selector: 'app-flat-data-table',
  template: '<div></div>',
})
class MockFlatDataTableComponent {
  @Input() periodId: number = 0;
  @Input() managedAccountId: string = '';
  @Input() moduleName: string = '';
  @Input() isMonthly: boolean = false;
  @Input() isQuarterly: boolean = false;
  @Input() isAnnually: boolean = false;
}

@Component({
  selector: 'app-static-data-table',
  template: '<div></div>',
})
class MockStaticDataTableComponent {
  @Input() periodId: number = 0;
  @Input() isMonthly: boolean = false;
  @Input() isQuarterly: boolean = false;
  @Input() isAnnually: boolean = false;
}

// Mock services
const mockManagedAccountService = {
  getManagedCapTableConfig: jasmine.createSpy('getManagedCapTableConfig').and.returnValue(
    of({
      capTablePeriods: [
        { periodId: 1, period: 'Jan 2024', isMonthly: true, isQuarterly: false, isAnnually: false },
        { periodId: 2, period: 'Q1 2024', isMonthly: false, isQuarterly: true, isAnnually: false },
        { periodId: 3, period: '2024', isMonthly: false, isQuarterly: false, isAnnually: true }
      ],
      latestPeriod: { periodId: 1, period: 'Jan 2024', isMonthly: true, isQuarterly: false, isAnnually: false }
    })
  ),
  getFootnote: jasmine.createSpy('getFootnote').and.returnValue(
    of({ footnoteContent: 'Test footnote content' })
  ),
  saveFootnote: jasmine.createSpy('saveFootnote').and.returnValue(
    of({ success: true })
  ),
  downloadTemplate: jasmine.createSpy('downloadTemplate').and.returnValue(
    of({})
  )
};

const mockMiscellaneousService = {
  downloadExcelFile: jasmine.createSpy('downloadExcelFile').and.returnValue(of({}))
};

const mockToastrService = {
  success: jasmine.createSpy('success'),
  error: jasmine.createSpy('error'),
  info: jasmine.createSpy('info'),
  warning: jasmine.createSpy('warning')
};

describe('ManagedAccountDataTableComponent', () => {
  let component: ManagedAccountDataTableComponent;
  let fixture: ComponentFixture<ManagedAccountDataTableComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [
        ManagedAccountDataTableComponent,
        MockDataTableFootnoteComponent,
        MockFlatDataTableComponent,
        MockStaticDataTableComponent
      ],
      imports: [
        KendoModule,
        BrowserAnimationsModule,
        ToastrModule.forRoot(),
        HttpClientTestingModule
      ],
      providers: [
        NgbModal,
        { provide: ManagedAccountService, useValue: mockManagedAccountService },
        { provide: MiscellaneousService, useValue: mockMiscellaneousService },
        { provide: ToastrService, useValue: mockToastrService }
      ]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ManagedAccountDataTableComponent);
    component = fixture.componentInstance;

    // Set required inputs before detectChanges to ensure ngOnInit runs with proper values
    component.managedAccountId = 'test-id';
    component.moduleName = 'test-module';

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should have injected services', () => {
    expect((component as any).managedAccountService).toBeDefined();
    expect((component as any)._miscService).toBeDefined();
  });

  it('should initialize with default values', () => {
    expect(component.tableTitle).toBe('');
    expect(component.tableName).toBe('');
    expect(component.footnotes).toBeDefined();
    expect(component.footnotes.length).toBe(1);
  });

  it('should accept input properties', () => {
    const testTitle = 'Test Table';
    const testData = [{ id: 1, name: 'Test' }];
    const testName = 'test_table';

    component.tableTitle = testTitle;
    component.tableName = testName;

    expect(component.tableTitle).toBe(testTitle);
    expect(component.tableName).toBe(testName);
  });
  
  
  it('should handle empty cap table periods', fakeAsync(() => {
    // Mock service to return empty periods
    mockManagedAccountService.getManagedCapTableConfig.and.returnValue(
      of({ capTablePeriods: [], latestPeriod: null })
    );

    // Recreate component with new mock to test empty periods scenario
    fixture = TestBed.createComponent(ManagedAccountDataTableComponent);
    component = fixture.componentInstance;
    component.managedAccountId = 'test-id';
    component.moduleName = 'test-module';
    fixture.detectChanges();

    tick();

    expect(component.hasCapTableData).toBe(false);
    expect(component.selectedPeriod).toBe(null);
    expect(component.periodId).toBe(0);
  }));

  it('should return correct disabled state for period combobox', () => {
    // Test when periodData is empty
    component.periodData = [];
    expect(component.isPeriodComboboxDisabled).toBe(true);

    // Test when periodData has items
    component.periodData = [{ value: 'test' }];
    expect(component.isPeriodComboboxDisabled).toBe(false);

    // Test when periodData is null
    component.periodData = null;
    expect(component.isPeriodComboboxDisabled).toBe(true);
  });

  it('should load footnote on initialization', () => {
    expect(mockManagedAccountService.getFootnote).toHaveBeenCalled();
  });

  it('should load cap table config on initialization', () => {
    expect(mockManagedAccountService.getManagedCapTableConfig).toHaveBeenCalledWith('test-id', 'test-module');
  });

  it('should handle file upload with valid Excel file', () => {
    const mockFile = new File(['test content'], 'test.xlsx', { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    const mockEvent = { target: { files: [mockFile] } };

    spyOn(component, 'handleFileUpload');
    component.onFileSelected(mockEvent);

    expect(component.handleFileUpload).toHaveBeenCalledWith(mockFile);
  });

  it('should handle file upload with invalid file type', () => {
    const mockFile = new File(['test content'], 'test.txt', { type: 'text/plain' });
    const mockEvent = { target: { files: [mockFile] } };

    component.onFileSelected(mockEvent);

    expect(mockToastrService.error).toHaveBeenCalledWith('Invalid file type. Please upload Excel files only.');
  });

  it('should handle file upload with oversized file', () => {
    // Create a file larger than 5MB
    const largeContent = new Array(6 * 1024 * 1024).fill('a').join('');
    const mockFile = new File([largeContent], 'large.xlsx', { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    const mockEvent = { target: { files: [mockFile] } };

    component.onFileSelected(mockEvent);

    expect(mockToastrService.error).toHaveBeenCalledWith('File size exceeds 5MB limit');
  });

  it('should download template when downloadTemplate is called', () => {
    component.downloadTemplate();

    expect(mockManagedAccountService.downloadTemplate).toHaveBeenCalled();
  });

  it('should call downloadExcelFile when template download is successful', fakeAsync(() => {
    const mockResponse = { data: 'mock-excel-data' };
    mockManagedAccountService.downloadTemplate.and.returnValue(of(mockResponse));

    component.downloadTemplate();
    tick(); // Ensure async operations complete

    expect(mockMiscellaneousService.downloadExcelFile).toHaveBeenCalledWith(mockResponse);
  }));

  it('should handle footnote operations', () => {
    const mockFootnote = { newComment: 'Test comment', isEdit: true };

    component.handleSave(mockFootnote);
    expect(mockManagedAccountService.saveFootnote).toHaveBeenCalled();

    component.handleCancel(mockFootnote);
    expect(mockFootnote.isEdit).toBe(false);

    component.handleReset(mockFootnote);
    expect(mockFootnote.newComment).toBe('');
  });

  it('should handle upload success', () => {
    spyOn(component as any, 'loadCapTableConfig');
    const mockResponse = { success: true };

    component.handleUploadSuccess(mockResponse);

    expect((component as any).loadCapTableConfig).toHaveBeenCalled();
  });

  it('should set period data correctly when cap table config is loaded', fakeAsync(() => {
    const mockConfig = {
      capTablePeriods: [
        { periodId: 1, period: 'Jan 2024', isMonthly: true, isQuarterly: false, isAnnually: false },
        { periodId: 2, period: 'Q1 2024', isMonthly: false, isQuarterly: true, isAnnually: false }
      ],
      latestPeriod: { periodId: 1, period: 'Jan 2024', isMonthly: true, isQuarterly: false, isAnnually: false }
    };

    mockManagedAccountService.getManagedCapTableConfig.and.returnValue(of(mockConfig));

    // Recreate component to test the full flow
    fixture = TestBed.createComponent(ManagedAccountDataTableComponent);
    component = fixture.componentInstance;
    component.managedAccountId = 'test-id';
    component.moduleName = 'test-module';
    fixture.detectChanges();

    tick();

    expect(component.hasCapTableData).toBe(true);
    expect(component.selectedPeriod).toEqual(mockConfig.latestPeriod);
    expect(component.periodId).toBe(1);
  }));
});
